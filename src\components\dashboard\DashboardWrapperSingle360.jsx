'use client'
import { useDashboardContext } from '@/libs/contextProviders/useContextDashboard'
import React, { useEffect, useState } from 'react'
import { BsTrash, BsTrash2 } from 'react-icons/bs'
import DashboardExperienceWorld from './DashboardExperienceWorld'
import DashboardInputWrapper from './DashboardInputWrapper'
import { useRouter } from 'next/navigation'
import { ACTIONS_DASHBOARD } from '@/libs/contextProviders/reducerDashboard'

export default function DashboardWrapperSingle360({data,dataList}) {
  const {
    dataObject,setDataObject,
    dashboardDispatchEdit,
    dataUpdate,setDataUpdate,
    markerObject,
    markerList,setMarkerList
  }=useDashboardContext()
  
  const router=useRouter()

  const restView = (params) => {
    dashboardDispatchEdit({type:ACTIONS_DASHBOARD.RESET_VIEW})
  }
  
  useEffect(() => {
    data && setDataObject(data)
  }, [data])
  
  useEffect(() => {
    dataList && setDataUpdate(dataList)
  }, [dataList])

  // console.log('DashboardWrapperSingle360:',markerObject)
  // console.log('DashboardWrapperSingle360:',markerList)
  
  return (
    <div className='flex relative w-full h-full justify-end shadow overflow-hidden'>
      <div className='flex relative w-2/3 h-full bg-gray-200'>
        <button onClick={restView} className='flex bg-gray-900 absolute top-2 cursor-pointer hover:border-2 border-gray-600 duration-200 ease-linear left-2 z-10 items-center justify-center round shadow-lg text-white capitalize p-3 max-h-10 px-4 text-xs rounded-md'>reset view</button>
        <div className='flex flex-col gap-1 z-10 absolute right-2 top-2 w-1/4 max-h-full'>
          {markerList?.map((i,index)=>
            <div key={index} className='flex text-xs items-center justify-center text-white rounded bg-black/50 w-full min-h-16 max-h-32 p-2'>
              {/* {console.log(i?.position)} */}
              <div className='flex flex-col w-full h-fit'>
                <span className='text-nowrap capitalize whitespace-break-spaces'>marker name:{i?.name}</span>
                <span className='text-nowrap capitalize whitespace-break-spaces'>location:{i?.location}</span>
                <span className='text-nowrap capitalize whitespace-break-spaces'>360 name:{i?._360Name}</span>
                <span className='text-nowrap capitalize whitespace-break-spaces'>posiition x,y,z:{i?.position}</span>
              </div>
              <BsTrash2 onClick={e=>setMarkerList(markerList?.filter(item=>item?.name!=i?.name))} className="flex text-xs text-gray-500 cursor-pointer min-h-full max-h-full px-2 rounded shadow border-2 border-gray-100"/>
            </div>
          )}
        </div>
        <DashboardExperienceWorld/>
      </div>
      <div className='flex w-1/3 h-full rounded-r-md p-2 overflow-hidden'>
        <DashboardInputWrapper/>
      </div>
    </div>
  )
}
