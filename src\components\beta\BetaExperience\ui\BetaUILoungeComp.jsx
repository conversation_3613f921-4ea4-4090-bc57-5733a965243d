import React from 'react'
import <PERSON>E<PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import BetaExperienceStairsPointComp from './BetaExperienceStairsPointComp'
import BetaExperienceInfoPointComp from './BetaExperienceInfoPointComp'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUILoungeComp({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const handleEntrance = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[0].name})
  }

  const handleDining = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[2].name})
  }

  const handleTable = (name) => {
    // console.log('BetaUILoungeComp handleTable:',settings._360s[0].list[7].name)
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[7].name})
  }

  const handleStairs = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[0].name})
  }
  const handleInfo = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
  }
  const handleInfoTV = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
  }
  return (
    <>
        {/* INFO BTN */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-10,3,-30]}
            occlude
        >
            <BetaExperienceInfoPointComp data={{height:settings.menuExp.readMore.height,width:settings.menuExp.readMore.width,image:settings.menuExp.readMore.btnIcons}} handleClick={handleInfo}/>
        </Html>
        {/* STAIRS */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-15,-5,-30]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.upstairs.height,width:settings.menuExp.upstairs.width,image:settings.menuExp.upstairs.btnIcons}} handleClick={handleStairs}/>
        </Html>
        {/* TABLE*/}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-4,-20,-30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleTable}/>
        </Html>
        {/* DOOR */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-2,-15,30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleEntrance}/>
        </Html>
        {/* DINING */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-60,-26,30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleDining}/>
        </Html>
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-60,-26,30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleDining}/>
        </Html>
    </>
  )
}
