import { useDashboardContextComponent } from '@/libs/contextProviders/useDashboardContextComponent'
import { uploadFiles } from '@/libs/firebase/firbaseFileFunctions'
import { settings } from '@/libs/siteSettings'
import { set } from 'mongoose'
import React, { useEffect, useState } from 'react'
import { BsTrash } from 'react-icons/bs'

function FileUpload() {
    const {
        dataArrayList,setDataArrayList
    }=useDashboardContextComponent()

    const [progress,setProgress]=useState(0)
    const [fileUploadArray,setFileUploadArray]=useState([])
    const [files,setFiles]=useState([])
    
    const fileUpload = () => {
        Object.values(files).map(i=>{
            const fileName=i.name.split('.')[0]
            const fileExtension=i.name.split('.')[1]
            uploadFiles(fileName,fileExtension,i,setProgress,setDataArrayList)
        })
    }

    useEffect(() => {
        fileUpload()
    }, [files])

    // console.log('FileUpload fileUploadArray:',fileUploadArray)
    return(
        <>
            <span className='text-xs'>{progress.toFixed(0)}% uploaded</span>
            <div className='flex flex-col w-full h-fit justify-end gap-2'>
                <div className={`flex w-full h-10 text-xs items-center rounded-md shadow px-2 ${settings.inputfields.inputCss}`}>
                    <input onChange={e=>setFiles(e.target.files)} type="file" multiple className={`flex w-full h-fit items-center`}/>
                </div>
                {/* <button onClick={e=>fileUpload()} className='flex text-xs flex-nowrap w-full px-4 h-10 capitalize hover:bg-gray-700 bg-gray-900 duration-200 ease-linear rounded-md text-white cursor-pointer items-center justify-center'>upload 360s</button> */}
            </div>
        </>
    )
}

export default function TestInputComponent() {
    const {
        locationList,
        setLocationList,
        dataArrayList,setDataArrayList,
        searchInput,setSearchInput,
        dbData,
        dataObject,setDataObject,
        textureId,setTextureId
    }=useDashboardContextComponent()

    const [error,setError]=useState('')
    const [showError,setShowError]=useState(false)
    const [progress,setProgress]=useState(0)
    const [input,setInput]=useState(null)
    
    // add to generate 360 location list
    const handleAddList = () => {
        !locationList?.find(({name})=>name==input) && setLocationList([...locationList,{name:input}])
    }
    
    // handle delete from 360 location list
    const handleDelete = (id) => {
        // console.log(id)
      setLocationList(locationList?.filter(i=>i?.name!==id))
    }
    
    // handle delete from 360 location list
    const handleSubmit =async (id) => {
        // console.log('TestInputComponent:',dataArrayList[0])
        dataArrayList?.map(i=>{
            // console.log(i)
            setShowError(false)
            try {
                fetch(`${settings.url}/api/360s`,{
                    method:'POST',
                    headers:{
                        'Content-Type':'application/json'
                    },
                    body:JSON.stringify(i)
                })
                setShowError(true)
                setError('success uploading')
            } catch (error) {
                setShowError(true)
                console.log('failed to submit',error)
                setError('failed to add the list to the server')
            }
        })
    }    
    
    
    // handle delete from 360 location list
    const handleUpdate =async () => {
        // console.log('TestInputComponent:',dataObject?._id)
        setShowError(false)
        try {
            fetch(`${settings.url}/api/360s/${dataObject?._id}`,{
                method:'PATCH',
                headers:{
                    'Content-Type':'application/json'
                },
                body:JSON.stringify(dataObject)
            })
            setShowError(true)
            setError('success uploading')
        } catch (error) {
            setShowError(true)
            console.log('failed to submit',error)
            setError('failed to add the list to the server')
        }
    }    
    
    // console.log('TestInputComponent:', dbData)
  return (
    <div className='flex w-full h-full flex-col gap-4 text-gray-600'>
        {/* 360 upload file section */}
        <div className='flex w-full h-fit bg-gray-100 rounded-md flex-col gap-3 shadow p-2'>
            <span className='text-2xl font-bold'>360 Upload</span>

            {/* file upload section */}
            <div className='flex flex-col gap-2 p-2 bg-gray-300 w-full h-fit rounded-md shadow'>
                <span className='text-xs font-medium'>Please select files below to upload</span>
                <FileUpload setProgress={setProgress}/>
            </div>
        </div>

        {/* 360 search section */}
        <div className='flex w-full h-fit bg-gray-100 rounded-md flex-col gap-3 shadow p-2'>
            <span className='text-2xl font-bold'>360 search</span>
            <div className='flex gap-2 p-2 bg-gray-300 w-full h-fit rounded-md shadow'>
                <input type="text" onChange={e=>setSearchInput(e.target.value)} placeholder="search" className={settings.inputfields.inputCss}/>
            </div>
        </div>

        {/* location title section */}
        <div className='flex w-full h-fit bg-gray-100 rounded-md flex-col gap-2 shadow p-2'>
            <span className='text-2xl mb-1 font-bold'>360 Upload</span>
            <div className='flex gap-2 p-2 bg-gray-300 w-full h-fit rounded-md shadow'>
                <div className='flex shadow w-full h-10 items-center rounded-md gap-2'>
                    <input onChange={e=>setInput(e.target.value)} type="text" placeholder="Enter location title" className={settings.inputfields.inputCss}/>
                </div>
                <button onClick={e=>handleAddList()} className='flex w-fit px-4 h-full capitalize hover:bg-gray-700 bg-gray-900 duration-200 ease-linear rounded-md text-white cursor-pointer items-center justify-center'>add</button>
            </div>
            <div className='flex flex-wrap gap-2 p-1 bg-gray-300 w-full min-h-2 max-h-20 rounded-md shadow overflow-y-auto'>
                {locationList.map((i,index)=>
                    <div key={index} className='flex relative w-fit h-8 gap-1 p-1 items-center border-2 rounded-md border-white'>
                        <span className='flex text-white h-full text-sm capitalize w-fit px-2 rounded-md'>{i?.name}</span>
                        <BsTrash onClick={e=>handleDelete(i?.name)} className='flex relative items-center justify-center text-xs text-white p-1 bg-gray-900 w-fit h-fit rounded border-2 border-white shadow cursor-pointer'/>
                    </div>
                )}
            </div>
        </div>
        {showError && <span className='flex items-center w-full text-xs font-bold text-gray-600'>{error}</span>}
        <button onClick={dbData ? e=>handleUpdate() : e=>handleSubmit()} className='flex w-full px-4 h-10 capitalize hover:bg-gray-700 bg-gray-900 duration-200 ease-linear rounded-md text-white cursor-pointer items-center justify-center'>Submit</button>
    </div>
  )
}
