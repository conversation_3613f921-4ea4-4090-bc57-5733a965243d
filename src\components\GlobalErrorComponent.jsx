import { settings } from '@/libs/siteSettings'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React from 'react'

export default function GlobalErrorComponent({reset}) {
  const router=useRouter()
  const refreshFunction = () => {
    if(location){
      location.reload(true)
    }
  }
  
  return (
    <div className='flex flex-col gap-3 max-w-[240px] w-fit px-5 h-fit items-center p-4 rounded-lg bg-gray-100 shadow-lg '>
      <Link href={`/`} className='flex items-center tracking-[4px] text-2xl capitalize'>{settings.siteName.name}</Link>
      <button
        className='flex underline text-sm font-light px-5'
          onClick={
          // Attempt to recover by trying to re-render the segment
          () => reset()
          }
      >
          Try again
      </button>
      <button
        className='flex underline text-sm font-light px-5'
          onClick={
          // Attempt to recover by trying to re-render the segment
          () => refreshFunction()
          }
      >
          refresh page
      </button>
    </div>
  )
}
