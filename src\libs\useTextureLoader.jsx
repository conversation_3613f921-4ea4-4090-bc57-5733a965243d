
export const useTextureLoader = (textureUrlList,textureLoader) => {
    // console.log('useTextureLoader textureUrlList',textureUrlList)
    // console.log('useTextureLoader textureLoader',textureLoader)
    let textureList=[]
    if(textureUrlList){
        textureUrlList?.map((item,index)=>{
            textureLoader?.load(
                item?.url,
                (texture)=>{
                    // texture.name=item[index]?.name
                    textureList?.push(texture)
                },
                (e)=>console.log(e),
                (error)=>console.log('errorloading texture')
            )
        })
    }
    return textureList
}
