import { Canvas } from '@react-three/fiber'
import React, { Suspense, useEffect, useRef, useState } from 'react'
import ExperienceLoader from './Components/BetaExperienceLoader'
import BetaExperience360 from './Components/BetaExperience360'

export default function BetaExperienceWorld({data}) {
  const [fov,setFov]=useState(60)
  const divRef=useRef(null)
  useEffect(()=>{
    setFov(divRef?.current?.clientWidth<460 ? 75 : 55)
  },[divRef.current])
    // console.log('ExperienceWorld:',data)
  return (
    <div 
      ref={divRef} 
      className='flex h-full w-full'>
      <Canvas
        // camera={{fov:fov}}
      >
        <Suspense
          fallback={<ExperienceLoader/>}
        >
          <BetaExperience360 data={data}/>
        </Suspense>
      </Canvas>
    </div>
  )
}
