'use client'
import { useDashboardContext } from '@/libs/contextProviders/useContextDashboard'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { BsTrash } from 'react-icons/bs'
import DashboardExperienceWorld from './DashboardExperienceWorld'
import DashboardInputWrapper from './DashboardInputWrapper'
import { useRouter } from 'next/navigation'

export default function DashboardWrapper({data}) {
  const {
    dataObject,setDataObject,
    dataUpdate,setDataUpdate,
    searchInput,setSearchInput,
    refresh360,setRefresh360
  }=useDashboardContext()
  
  const router=useRouter()

  // console.log('DashboardWrapper:',data)

  // this deletes a particular item in a list
  const delete360 = (id) => {
    setDataUpdate(dataUpdate?.filter(i=>i?.name!=id))
  }

  // this finds a particular item in a list
  const selectId = (id) => {
    setDataObject(dataUpdate?.find(({_id})=>_id==id))
    setRefresh360(!refresh360)
  }  
  
  // this updates the dasboard list with the server findings
  useEffect(() => {
    data && setDataUpdate(data)
  }, [data])

  // console.log('DashboardWrapper:',dataObject)
  
  return (
    <div className='flex relative w-full h-full justify-end shadow'>
      <div className='flex flex-col gap-1 w-2/12 h-full ml-2 overflow-y-auto overflow-hidden'>
        <div className='flex flex-col w-full h-full overflow-hidden my-2 rounded-l-md overflow-y-auto gap-2'>
          {dataUpdate?.map((i,index)=>
          // {dataUpdate?.filter(i=>i?.name==searchInput).map((i,index)=>
            <div key={index} className="flex relative min-w-full bg-gray-900 max-h-28 min-h-32 cursor-pointer overflow-hidden rounded-md">
              <Image onDoubleClick={e=>router.push(`/dashboard/${dataObject?._id}`)} onClick={e=>selectId(i?._id)} alt="360 iamges" priority src={i?.url} className="scale-100 hover:scale-105 duration-200 ease-linear object-cover select-none hover:brightness-110 brightness-75"  width={3040} height={1520}/>
              <div className="flex absolute top-2 left-2 w-fit h-fit">
                <span className='text-xs p-1 rounded bg-black/50- capitalize font-bold text-white'>{i?.name}</span>
              </div>
              <div onClick={e=>delete360(i?.name)} className="flex p-1 bg-gray-200 hover:broder-2 duration-200 ease-linear border-gray-500 absolute bottom-2 left-0 right-0 mx-auto w-fit h-fit shadow cursor-pointer rounded-md">
                <BsTrash className='text-lg'/>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className='flex w-7/12 h-full bg-gray-200'>
        <DashboardExperienceWorld/>
      </div>
      <div className='flex w-3/12 h-full rounded-r-md p-2'>
        <DashboardInputWrapper data={data}/>
      </div>
    </div>
  )
}
