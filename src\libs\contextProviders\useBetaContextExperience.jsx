'use client'
import { createContext, useContext, useReducer, useState } from "react"
import { INITIAL_EXPERIENCE_STATE, reducerBetaExperience, } from "./reducerBetaExperience"

const BetaExperienceContext=createContext()

export default function BetaExperienceContextProvider({children}) {
    const [experienceState,experienceDispatch]=useReducer(reducerBetaExperience,INITIAL_EXPERIENCE_STATE)
    const [webGlCompatibilty,setWebGlCompatibilty]=useState(true)
    const [shwoBooknowMenu,setShwoBooknowMenu]=useState(false)
    return (
        <BetaExperienceContext.Provider
            value={{experienceState,experienceDispatch,webGlCompatibilty,setWebGlCompatibilty,shwoBooknowMenu,setShwoBooknowMenu}}
        >
            {children}
        </BetaExperienceContext.Provider>
    )
}

export const useBetaContextExperience=()=>{
    const context=useContext(BetaExperienceContext)
    if(!context){
        throw new Error('useExperienceContext must be used within an ExperienceProvider')
    }
    return context
}