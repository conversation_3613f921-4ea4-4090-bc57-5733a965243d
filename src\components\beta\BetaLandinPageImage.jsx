'use client'

import { useBetaContextExperience } from "@/libs/contextProviders/useBetaContextExperience"
import Image from "next/image"
import ImageContainer from "../ImageContainer"
import LandingpageComponent from "./BetaLandingpageComponent"

export default function BetaLandinPageImage() {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  // console.log('LandinPageImage:')
  return (
    <>
      {experienceState?.landinPageItems && <div className='z-20 landinPageImage flex w-full h-full fixed top-0 left-0 bg-black text-white'>
        <div className="flex w-full h-full relative">
          <ImageContainer data={'/hero_image_001.jpg'}/>
          <LandingpageComponent/>
        </div>
      </div>} 
    </>
  )
}
