import React, { useEffect } from 'react'
import Experience360 from './Experience360'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { useLoader } from '@react-three/fiber'
import { TextureLoader } from 'three'

export default function ExperienceTextureLoader({setShowLoader}) {
    const {
      experienceStateList,setExperienceStateList,
      dataObject,setDataObject,
      dataUpdate,setDataUpdate,
    }=useExperienceContext()
    
    console.log(dataUpdate)

    let dataArray=[dataUpdate]

    let textureArrayList=[]
    let texturePathList=[]
    let textureNameList=[]

    // dataArray?.map(i=>{
    //   textureArrayList.push(i?.name)
    //   texturePathList.push(i?.url)
    //   textureNameList.push(i?.name)
    // })

    dataUpdate?.map(i=>{
      textureArrayList.push(i?.name)
      texturePathList.push(i?.url)
      textureNameList.push(i?.name)
    })

    textureArrayList=useLoader(TextureLoader,texturePathList)

    textureArrayList.map((i,index)=>i.name=textureNameList[index])

    // console.log('ExperienceTextureLoader:',textureArrayList[0])
  return (
    <>
      <Experience360 setShowLoader={setShowLoader} texture={textureArrayList[0]}/>
    </>
  )
}
