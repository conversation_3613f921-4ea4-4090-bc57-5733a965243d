'use client'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import Image from 'next/image'

export default function BetaMovement360Comp({handleClick}) {
  // const {experienceState,experienceDispatch}=useExperienceContext()
  // const handleClick = () => {
  //   // experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:'entrance'})
  //   // experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:'lounge'})
  // }
  
  // console.log('Experience360Comp:',)
  return (
      <div className="flex flex-col items-center select-none absolute text-center left-0 right-0 bottom-24 mx-auto bg-black/75' justify-end w-[332px] h-[541px] bg-black/25' p-2">
        <div className="flex relative max-w-fit max-h-fit">
          <Image width={271} height={271} alt='swipe info icon' src={'/elephant_island_logo.png'}/>
        </div>

        <div className="flex relative items-center  text-center flex-col uppercase mb-4">
          <span className="font-medium text-nowrap text-3xl">your safari holiday</span>
          <span className="font-bold text-5xl">destination</span>
        </div>

        <div className="flex relative max-h-fit max-w-fit rounded-full">
          <Image width={86} height={87} alt='swipe info icon' src={'/swipe_icon.png'}/>
        </div>

        <span className="font-bold tracking-tighter text-center text-lg uppercase mb-6">look around and explore</span>

        <button className="flex text-gray-700 text-center items-center justify-center text-lg uppercase bg-gray-50 shadow font-medium min-h-12 rounded-xl px-12 shadow-gray-50" 
          onClick={handleClick}
        >
          step inside
        </button>
      </div>
  )
}