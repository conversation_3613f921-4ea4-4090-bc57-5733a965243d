'use client'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { settings } from '@/libs/betasiteSettings'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaBooknowMenuPopup() {
  const {shwoBooknowMenu,setShwoBooknowMenu}=useBetaContextExperience()
  // console.log('MenuPopup:',experienceState?.showInfo)
  return (
    (shwoBooknowMenu && <div className='booknowWrapper flex z-40 absolute top-0 left-0 h-full w-full overflow-hidden bg-black/80'>      
        <div 
            onClick={e=>setShwoBooknowMenu(!shwoBooknowMenu)} 
            className=" flex z-10 items-center justify-center absolute top-0 right-[96px] h-[75px] w-[96px] cursor-pointer"
        >
          <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
        </div>
        <div className='flex z-50 flex-col absolute top-[75px] left-0 max-h-fit min-w-fit p-[75px] overflow-hidden'>
          <div className='flex w-full h-auto'>
              <Image className='image object-left' src={'/booking_form_001.png'} alt='background image' width={891} height={600}/>
          </div>
        </div>
      </div>)
  )
}
