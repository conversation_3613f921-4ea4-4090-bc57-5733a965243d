'use client'

import { useExperienceContext } from "@/libs/contextProviders/useBetaContextExperience"
import { uploadFiles } from "@/libs/firebase/firbaseFileFunctions"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

function InputText({inputArgs,projectTitle}) {
  const {userInfoDetails,setUserInfoDetails,fileArray,setFileArray}=useExperienceContext()
  const pathname=usePathname()
  const handleChange=(e)=>{
    setUserInfoDetails(prev=>prev={...prev,[inputArgs?.name]:e.target.value})
  }
  // console.log('InputText:',projectTitle)
  return(
    (!projectTitle && <input onChange={handleChange} className='flex w-full px-2 outline-none bg-gray-100 placeholder:text-sm text-sm min-h-10 rounded-md shadow-md' {...inputArgs}/>)
  )
}

function InputFiles({inputArgs,projectTitle,setUploadComplete}) {
  const {userInfoDetails,setFileArray}=useExperienceContext()
  const [progress,setProgress]=useState()
  const [pending,setPending]=useState(false)
  const [files,setFiles]=useState([])

  useEffect(() => {
    files?.length>0 && handleUpload()
  },[files])

  useEffect(() => {
    // handleUpload()
  },[progress>99])
  
  const handleUpload = () => {
    console.log('running uploade fn')
    files?.map((item)=>{
      const name=item?.name?.split('.')[0]
      const extension=item?.name?.split('.')[1]
      uploadFiles(userInfoDetails?.projectTitle,name,extension,item,setProgress,setFileArray,setUploadComplete)
    })
  }

  const handleChange = (e) => {
    setFiles(Object.values(e.target.files))
  }

  // console.log('InputFiles:',files)
  return(
    <div className="flex w-full h-fit flex-col bg-gray-100  rounded-md shadow-md p-2">
      <div className="flex items-center w-fit gap-4">
        <span className="font-medium capitalize">{inputArgs?.name}</span>
        {progress>0 && <div className="flex items-center gap-4 text-sm font-medium">
          {progress>0 && progress<99 && <span>{progress.toFixed(2)}% uploading...</span>}
          {progress==100 && <span>upload complete</span>}
        </div>
        }
      </div>
      {projectTitle?.length>0 || userInfoDetails?.projectTitle?.length>0
        ? <input multiple onChange={(e)=>handleChange(e)} className='flex w-full mt-1 outline-none  placeholder:text-sm text-sm' {...inputArgs}/>
        : <span className="font-medium text-sm italic">enter {[inputArgs?.name]} to upload files</span>
      }
    </div>
  )
}

export default function DashboardInputComponent({input,data}) {
  const [uploadComplete,setUploadComplete]=useState(false)
  const [fileUploadString,setFileUploadString]=useState()
  const pathname=usePathname()
  const {userInfoDetails,setUserInfoDetails,fileArray,setFileArray,dataExperience,setDataExperience}=useExperienceContext()
  const projectTitle=data?.projectTitle

  useEffect(()=>{
    dataExperience ? setFileArray(dataExperience?.projectFiles) : setFileArray([])
  },[dataExperience])

  const handleUpdate = (e) => {
      e.preventDefault()
      console.log('handleUpdate')
  }
  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('handleSubmit')
  }
  // console.log('DashboardInputComponent:',{projectFiles:dataExperience?.projectFiles},{fileArray:fileArray})
  return (
    <form
      className="flex w-full h-fit flex-col gap-3 text-gray-500"
      onSubmit={pathname?.split('/')[2]==='add' ? handleSubmit : handleUpdate}
    >
      {input.inputTest.map((input,index)=>
        <InputText 
          key={index}
          inputArgs={input}
          projectTitle={projectTitle}
        />
      )}
      {input.inputFiles.map((input,index)=>
        <InputFiles 
          key={index} 
          inputArgs={input}
          projectTitle={projectTitle}
          setUploadComplete={setUploadComplete}
          setFileUploadString={setFileUploadString}
        />
      )}
      <input className='flex cursor-pointer w-full min-h-12 items-center justify-center text-white bg-gray-900 rounded-md shadow-md' placeholder='submit' type="submit" />
    </form>
  )
}
