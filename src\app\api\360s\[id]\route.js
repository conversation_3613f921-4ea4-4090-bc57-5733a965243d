import { _360Settings } from "@/libs/mongoDb/models/_360Settings";
import { connectToElephantislandDB } from "@/libs/mongoDb/connectToElephantislandDB";
import { NextResponse } from "next/server";

export async function GET(req,{ params }) {
    const { id } = await params
    // console.log('site api route',id)
    connectToElephantislandDB()
    try {
        const _360=await _360Settings.findById(id)
        return NextResponse.json(_360,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get 360s',{status:500})
    }
}

export async function PATCH(req,{params}) {
    const { id } = await params
    const body=await req.json()
    console.log('site api route',body,id)
    connectToElephantislandDB()
    try {
        await _360Settings.findByIdAndUpdate(id,body)
        return NextResponse.json('successfuly updated 360s',{status:201},{new:true})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get 360s',{status:500})
    }
}