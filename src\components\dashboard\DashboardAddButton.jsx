'use client'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import React from 'react'

export default function DashboardAddButton() {
    const pathname=usePathname()
    const router=useRouter()
    // console.log('DashboardAddButton:',pathname?.split('/'))
  return (
    <>
      {pathname?.split('/')?.length-1>1
        ? <button onClick={()=>router.back()} className='flex cursor-pointer bg-gray-900 border-2 border-gray-50 round shadow-lg text-white uppercase p-2 px-6 rounded-md' href={`/${pathname?.split('/')[1]}/add`}>back</button>  : null
      }
    </>
  )
}
