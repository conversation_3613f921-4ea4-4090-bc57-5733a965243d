'use client'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import ImageContainer from './ImageContainer'
import Image from 'next/image'
import ImageScalerComponent from './ImageScalerComponent'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function LogoComponent() {
    const {experienceState,experienceDispatch}=useBetaContextExperience()
    // const handleClick = (params) => {
    //   experienceDispatch({type:ACTIONS_EXPERIENCE.RESET})
    // }
    
  return (
    <div 
      // onClick={handleClick} 
      className='flex relative items-center justify-start h-fit w-fit'
    >
      <Image height={75} width={266} alt='logo image' src={'/elephant_island_logo_white_for_nav_bar.png'}/>
    </div>
  )
}
