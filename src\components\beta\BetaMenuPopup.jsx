'use client'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { settings } from '@/libs/betasiteSettings'
import Image from 'next/image'
import ImageScalerComponent from '../ImageScalerComponent'
import { useEffect, useRef, useState } from 'react'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'
import MenuMessage from './BetaMenuMessage'

function BtnLandingpageComponent({data,fn,fnEntrance,fnHome,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',data?.naem,index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            onClick={data?.name=='home' ? fnHome : data?.name=='entrance' ? fnEntrance : ()=>fn(data?.name,data?.location)}
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function BetaMenuPopup() {
  const lineClass2='w-full border-1 border-gray-400/30'
  const lineClass='w-full border-1 mt-2 border-gray-400/30'
  const refGroup=useRef()
  const {experienceState,experienceDispatch}=useBetaContextExperience()

  const handleHome= () => {
    // console.log('home')
    experienceDispatch({type:ACTIONS_EXPERIENCE.RESET})
  }

  const handleEntrance= () => {
    // console.log('home')
    experienceDispatch({type:ACTIONS_EXPERIENCE.ENTRANCE})
  }
  

  const handle360 = (name,location) => {
    // console.log('handle360',name)
    experienceDispatch({type:ACTIONS_EXPERIENCE._360_NAME,payload:location})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:name})
  }
  
  // console.log('MenuPopup:',experienceState?.showInfo)
  return (
    <>
      <div ref={refGroup} className={`menuPopup ${experienceState?.showMenu ? 'top-0' : 'top-[140%]'} overflow-y-auto z-20 duration-200 ease-linear flex absolute w-full h-full overflow-hidden bg-black/75`}>
        {experienceState?.showInfo
          ? <MenuMessage/>
          : <>
              <div className='flex absolute top-[75px] left-12 w-full justify-start h-[calc(100%-75px)] overflow-hidden overflow-y-auto'>
                <div className='flex flex-col mt-10 w-40 h-fit gap-[2px]'>
                  <hr className={lineClass2}/>
                  <div className='flex w-full h-fit'>
                    {settings.menuPoup.home.map((i,index)=>
                      <BtnLandingpageComponent key={index} index={index} fnEntrance={handleEntrance} fnHome={handleHome} data={i}/>
                    )}
                  </div>
                  <hr className={lineClass2}/>
                  {settings.menuPoup.entrance.map((i,index)=>
                    <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
                  )}
                  <hr className={lineClass}/>
                  {settings.menuPoup.firstFloor.map((i,index)=>
                    <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
                  )}
                  <hr className={lineClass}/>
                  {settings.menuPoup.outDoors.map((i,index)=>
                    <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
                  )}
                  <hr className={lineClass}/>
                  {settings.menuPoup.campOutskirts.map((i,index)=>
                    <BtnLandingpageComponent fn={handle360} fnEntrance={handleEntrance} fnHome={handleHome} index={index} key={index} data={i}/>
                  )}
                  <hr className={lineClass}/>
                </div>
              </div>
          </>
        }
      </div>
      {(experienceState?.showNav && experienceState?._360Name!=="entrance" && experienceState?._360Name!=="dining2") && <div className={`title flex z-50 absolute top-[75px] right-0 items-center justify-end min-w-48 max-w-fit text-end text-white h-[75px] from-black bg-gradient-to-l`}>
        <span className='flex items-center justify-center mr-6 text-2xl uppercase'>
          {/* {console.log('title:',experienceState?._360Name?.includes('lou'))} */}
          {
            experienceState?._360Name?.includes('lou') 
              ? 'lounge' : 
              experienceState?._360Name?.includes('din')
              ? 'dining': 
              experienceState?._360Name?.includes('bedroom 1')
              ? 'bedroom 1' : 
              experienceState?._360Name?.includes('bedroom 2')
              ? 'bedroom 2' : 
              experienceState?._360Name?.includes('mast')
              ? 'master bedroom' : experienceState?._360Name
          }
        </span>
      </div>}
    </>
  )
}
