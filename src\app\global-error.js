'use client'
import GlobalErrorComponent from "@/components/GlobalErrorComponent"

 // Error boundaries must be Client Components
 
export default function GlobalError({ error, reset }) {
  return (
    // global-error must include html and body tags
    <html>
      <body className="flex h-full w-full items-center justify-center flex-col gap-4">
        <GlobalErrorComponent error={error} reset={reset}/>
      </body>
    </html>
  )
}