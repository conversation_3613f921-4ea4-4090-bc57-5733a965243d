import { useDashboardContextComponent } from '@/libs/contextProviders/useDashboardContextComponent'
import { settings } from '@/libs/siteSettings'
import { set } from 'mongoose'
import React, { useEffect, useState } from 'react'
import { BsTrash } from 'react-icons/bs'

export default function TestInput360SettingsComponent() {
    const {
      locationList,setLocationList,
      dataArrayList,_360ArrayList,
      markerObject,setMarkerObject,
      markerList,setMarkerList
    }=useDashboardContextComponent()

    const [input,setInput]=useState(null)

    const handleAddList = () => {
      !locationList?.find(({name})=>name==input) && setLocationList([...locationList,{name:input}])
    }

    const handleMarkerList = () => {
      setMarkerList([...markerList,input])
    }

    const handleDelete = (id) => {
      setLocationList(locationList?.filter(i=>i?.name!==id))
    }

    const handleItemChange = (obj,id) => {
      // console.log('handleItemChange:',obj,id)
      const indexOfObject=markerList.findIndex(({name})=>name===id)
      console.log('handleItemChange index:',indexOfObject)
      setMarkerObject({...markerObject,...markerList.find(({name})=>name===id),...obj})
      console.log('handleItemChange markerObject:',markerObject)
      setMarkerList([markerList[indexOfObject]=markerObject])
    }
    
    console.log('TestInputComponent:',markerList)
    console.log('TestInputComponent:',markerObject)
  return (
    <div className='flex w-full h-full flex-col gap-4 text-gray-600'>

        {/* location title section */}
        <div className='flex w-full h-fit bg-gray-100 rounded-md flex-col gap-2 shadow p-2'>
            <span className='text-2xl mb-1 font-bold'>360 Upload</span>

            {/* location title input */}
            <div className='flex gap-2 p-2 bg-gray-300 w-full h-fit rounded-md shadow'>
              <div className='flex shadow w-full h-10 items-center rounded-md gap-2'>
                <input onChange={e=>setInput(e.target.value)} type="text" placeholder="Enter location title" className={settings.inputfields.inputCss}/>
              </div>
              <button onClick={e=>handleAddList()} className='flex w-fit px-4 h-full capitalize hover:bg-gray-700 bg-gray-900 duration-200 ease-linear rounded-md text-white cursor-pointer items-center justify-center'>add</button>
            </div>

            {/* gnerated location title */}
            <div className='flex flex-wrap gap-2 p-1 bg-gray-300 w-full min-h-2 max-h-20 rounded-md shadow overflow-y-auto'>
              {locationList.map((i,index)=>
                <div key={index} className='flex relative w-fit h-8 gap-1 p-1 items-center border-2 rounded-md border-white'>
                  <span className='flex text-white h-full w-fit px-2 rounded-md'>{i?.name}</span>
                  <BsTrash onClick={e=>handleDelete(i?.name)} className='flex relative items-center justify-center text-xs text-white p-1 bg-gray-900 w-fit h-fit rounded border-2 border-white shadow cursor-pointer'/>
                </div>
              )}
            </div>
        </div>

        {/* location title section */}
        <div className='flex w-full h-fit bg-gray-100 rounded-md flex-col gap-2 shadow p-2'>
            <span className='text-2xl mb-1 font-bold'>Marker Settings</span>

            {/* Marker title input */}
            <div className='flex gap-2 p-2 bg-gray-300 w-full h-fit rounded-md shadow'>
              <div className='flex shadow w-full h-10 items-center rounded-md gap-2'>
                <input onChange={e=>setInput({name:e.target.value})} type="text" placeholder="Marker name" className={settings.inputfields.inputCss}/>

                {/* select marker type*/}
                <select onChange={e=>setInput({...input,markerType:e.target.value})} className={settings.inputfields.inputCss}>
                  <option className='text-xs w-full h-10' value="">select marker type</option>
                  {settings.inputfields.markerType.map((i,index)=><option key={index} className='text-xs w-full h-10' value={i}>{i}</option>)}
                </select>
              </div>

              {/* add to list button */}
              <button onClick={e=>handleMarkerList()} className='flex w-fit px-4 h-full capitalize hover:bg-gray-700 bg-gray-900 duration-200 ease-linear rounded-md text-white cursor-pointer items-center justify-center'>add</button>
            </div>

            {/* gnerated location title */}
            <div className='flex flex-wrap gap-2 p-1 bg-gray-300 w-full h-fit rounded-md shadow overflow-y-auto'>
              {markerList?.map((i,index)=>
                <div key={index} className='flex flex-col relative w-full h-fit gap-1 p-1 items-center border-2 rounded-md border-white'>
                  <div className='flex w-full h-fit items-center justify-between gap-2'>
                    <span className='flex capitalize text-sm h-full w-fit rounded-md'>{i?.name}</span>
                    <BsTrash onClick={e=>setMarkerList(markerList?.filter(i=>i?.name!==i?.name))} className='flex relative items-center justify-center text-xs text-white p-1 bg-gray-900 w-fit h-fit rounded border-2 border-white shadow cursor-pointer'/>
                  </div>
                  <hr className='flex w-full border-1 border-white'/>
                  <div className='flex flex-col w-full h-fit items-center rounded-md gap-2'>
                    <div className='flex w-full h-fit py-1 items-center justify-between rounded-md gap-2'>
                      <span className='text-xs'>market type:</span>
                      <span className='text-xs'>{i?.markerType}</span>
                    </div>
                    <div className='flex w-full h-10 items-center justify-between rounded-md gap-2'>

                      {/* select marker location*/}
                      <select onChange={e=>handleItemChange({location:e.target.value},i?.name)} className={settings.inputfields.inputCss}>
                        <option className='text-xs w-full h-10' value="">select location</option>
                        {settings.inputfields.locationsList.map((item,index)=><option key={index} className='text-xs w-full h-10' value={item}>{item}</option>)}
                      </select>

                      {/* select 360 or type of info graphic */}
                      {i?.markerType=='landingPage' || i?.markerType=='positionMarker' || i?.markerType=='levelMarker'
                        
                        ? <select onChange={e=>handleItemChange({_360Name:e.target.value},i?.name)} className={settings.inputfields.inputCss}>
                            <option className='text-xs w-full h-10' value="">select 360</option>
                            {_360ArrayList.map((item,index)=><option key={index} className='text-xs w-full h-10' value={item?.name}>{item?.name}</option>)}
                          </select>
                        : <select onChange={e=>handleItemChange({markerType:e.target.value},i?.name)} className={settings.inputfields.inputCss}>
                            <option className='text-xs w-full h-10' value="">select popup type</option>
                            {settings.inputfields.contentType.map((item,index)=><option key={index} className='text-xs w-full h-10' value={item}>{item}</option>)}
                          </select>
                      }
                    </div>
                  </div>
                </div>
              )}
            </div>
        </div>
    </div>
  )
}
