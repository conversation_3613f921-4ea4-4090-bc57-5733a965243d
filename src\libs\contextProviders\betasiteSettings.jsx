import { IoIosSearch } from "react-icons/io";
import { MdFavorite } from "react-icons/md";
import { FaLocationDot } from "react-icons/fa6";
import { FaPhoneAlt } from "react-icons/fa";
import { MdOutlineEmail } from "react-icons/md";
import { FaFacebookF } from "react-icons/fa";
import { FaInstagram } from "react-icons/fa";
import { FaPinterest } from "react-icons/fa";
import { FaYoutube } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { IoHomeOutline, IoStatsChartOutline } from "react-icons/io5";
import { IoAnalytics } from "react-icons/io5";
import { AiOutlineShop, AiOutlineUser } from "react-icons/ai";
import { LiaDollarSignSolid } from "react-icons/lia";
import { IoMailOutline } from "react-icons/io5";
import { HiOutlineChartPie } from "react-icons/hi";
import { MdOutlineDynamicFeed } from "react-icons/md";
import { MdOutlineMessage } from "react-icons/md";
import { MdOutlineManageAccounts } from "react-icons/md";
import { TbReportSearch } from "react-icons/tb";
import { BsFilesAlt } from "react-icons/bs";

export const settings={
    url:process.env.NODE_ENV=='production' ? 'https://digitalshiftinteractive.com' : 'http://localhost:3001',
    // url:'http://localhost:4000',
    siteName:'luyari',
    auth:{
        login:[
            {id:0,type:'email',placeholder:'Email',name:'email'},
            {id:1,type:'password',placeholder:'password',name:'password'},
            {id:2,type:'submit',placeholder:'submit'},
        ],
        register:[
            {id:0,type:'text',placeholder:'name',name:'name'},
            {id:1,type:'text',placeholder:'surname',name:'surname'},
            {id:2,type:'text',placeholder:'username',name:'username'},
            {id:3,type:'number',placeholder:'number',name:'number'},
            {id:4,type:'email',placeholder:'email',name:'email'},
            {id:5,type:'password',placeholder:'password',name:'password'},
            {id:6,type:'password',placeholder:'re-enter password',name:'confirmPassword'},
            {id:7,type:'submit',placeholder:'submit'},
        ],
    },
    collections:[
        {name:'view all'},
        {name:'by size',listWrapList:[
                {
                    label:'plans by area',
                    list:[
                        'under 100m2',
                        '100m2 to 300m2',
                        '300m2 to 500m2',
                        '500m2 to 700m2',
                        'over 750m2',
                    ]
                },
                {
                    label:'plans by bedroomes',
                    list:[
                        '1 bedrooms',
                        '2 bedrooms',
                        '3 bedrooms',
                        '4+ bedrooms',
                    ]
                },
                {
                    label:'plans by floors',
                    list:[
                        '1 floor',
                        '2 floors',
                        '3+ floors',
                    ]
                }
            ]
        },
        {name:'by budget',listWrapList:[
                {
                    label:'plans by floors',
                    list:[
                        'under $100',
                        '$100 to $200',
                        '$200 to $300',
                        '$300 to $400',
                        '$400 to $700',
                        'over $700',
                    ]
                }
            ]
        },
        {name:'custom-plan'},
    ],
    linkBackend:[
        {name:'dashboard'},
        // {name:'clientArea'}
    ],
    socials:[
        {id:0,icon:<FaFacebookF className="w-6 h-6" />},
        {id:1,icon:<FaInstagram className="w-6 h-6" />},
        {id:2,icon:<FaPinterest className="w-6 h-6" />},
        {id:3,icon:<FaYoutube className="w-6 h-6" />},
        {id:4,icon:<FaXTwitter className="w-6 h-6" />},
    ],
    dashboard:[
        {
            name:'dashboard',
            list:[
                {id:0,icon:<IoHomeOutline className="w-6 h-6" />,name:'home'},
                {id:1,icon:<IoAnalytics className="w-6 h-6" />,name:'analytics'},
                // {id:2,icon:<IoAnalytics className="w-6 h-6" />,name:'sales'},    
            ],
        },
        {
            name:'quick menu',
            list:[
                {id:2,icon:<AiOutlineUser className="w-6 h-6" />,name:'users'},
                {id:3,icon:<AiOutlineShop className="w-6 h-6" />,name:'products'},
                {id:3,icon:<BsFilesAlt className="w-6 h-6" />,name:'clientProjects'},
                {id:4,icon:<LiaDollarSignSolid className="w-6 h-6" />,name:'transactions'},
                {id:5,icon:<HiOutlineChartPie className="w-6 h-6" />,name:'reports'},
            ],
        },
        {
            name:'notifcations',
            list:[
                {id:6,icon:<IoMailOutline className="w-6 h-6" />,name:'mail'},
                {id:7,icon:<MdOutlineDynamicFeed className="w-6 h-6" />,name:'feedback'},
                {id:8,icon:<MdOutlineMessage className="w-6 h-6" />,name:'messages'},
            ],
        },
        {
            name:'admin',
            list:[
                {id:9,icon:<MdOutlineManageAccounts className="w-6 h-6" />,name:'manage'},
                {id:11,icon:<TbReportSearch className="w-6 h-6" />,name:'reports'},
            ],
        },
    ],
    dashboardClient:[
        // {
        //     name:'dashboard',
        //     list:[
        //         {id:0,icon:<IoHomeOutline className="w-6 h-6" />,name:'home'},   
        //     ],
        // },
        {
            name:'quick menu',
            list:[
                {id:4,icon:<IoStatsChartOutline className="w-6 h-6" />,name:'summary'},
                {id:5,icon:<HiOutlineChartPie className="w-6 h-6" />,name:'reports'},
            ],
        },
        {
            name:'notifcations',
            list:[
                // {id:6,icon:<IoMailOutline className="w-6 h-6" />,name:'mail'},
                // {id:7,icon:<MdOutlineDynamicFeed className="w-6 h-6" />,name:'feedback'},
                {id:8,icon:<MdOutlineMessage className="w-6 h-6" />,name:'messages'},
            ],
        },
        {
            name:'admin',
            list:[
                {id:9,icon:<MdOutlineManageAccounts className="w-6 h-6" />,name:'manage'},
                // {id:10,icon:<IoAnalytics className="w-6 h-6" />,name:'analytics'},
                {id:11,icon:<TbReportSearch className="w-6 h-6" />,name:'reports'},
            ],
        },
    ],
    auth:{
        login:[
            {id:0,type:'email',placeholder:'Email',name:'email'},
            {id:1,type:'password',placeholder:'password',name:'password'},
            {id:2,type:'submit',placeholder:'submit'},
        ],
        register:[
            {id:0,type:'text',placeholder:'name',name:'name'},
            {id:1,type:'text',placeholder:'surname',name:'surname'},
            {id:2,type:'text',placeholder:'username',name:'username'},
            {id:3,type:'number',placeholder:'number',name:'number'},
            {id:4,type:'email',placeholder:'email',name:'email'},
            {id:5,type:'password',placeholder:'password',name:'password'},
            {id:6,type:'password',placeholder:'re-enter password',name:'confirmPassword'},
            {id:7,type:'submit',placeholder:'submit'},
        ]
    },
    _360s:[
        {
            name:'entrance',
            list:[
                {name:'entrance',url:'./360s/entrance_360s/entrance.jpg',rotX:0,rotY:90},
                {name:'lounge',url:'./360s/entrance_360s/lounge.jpg',rotX:240,rotY:230},
                {name:'dining',url:'./360s/entrance_360s/dining_001.jpg',rotX:90,rotY:270},
                {name:'dining2',url:'./360s/entrance_360s/dining_002.jpg',rotX:90,rotY:290},
                {name:'patio right',url:'./360s/entrance_360s/patio_002.jpg',rotX:90,rotY:180},
                {name:'patio left',url:'./360s/entrance_360s/patio_003.jpg',rotX:90,rotY:90},
                {name:'',url:'./360s/entrance_360s/dining_002.jpg',rotX:90,rotY:290},
            ]
        },
        {
            name:'first floor',
            list:[
                {name:'first floor',url:'./360s/first_floor_360s/first_floor.jpg',rotX:0,rotY:245},
                {name:'bedroom 1',url:'./360s/first_floor_360s/bedroom_1.jpg',rotX:0,rotY:305},
                {name:'bedroom 2',url:'./360s/first_floor_360s/bedroom_2.jpg',rotX:0,rotY:126},
                {name:'master bedroom',url:'./360s/first_floor_360s/master_bedroom.jpg',rotX:0,rotY:100},
                {name:'balcony',url:'./360s/first_floor_360s/balcony.jpg',rotX:0,rotY:245},
            ]
        },
        {
            name:'the outdoors',
            list:[
                {name:'the outdoors',url:'./360s/the_outdoors_360s/the_outdoors.jpg',rotX:0,rotY:225},
                {name:'terrace',url:'./360s/the_outdoors_360s/terrace.jpg',rotX:0,rotY:225},
                {name:'west view',url:'./360s/the_outdoors_360s/west_view.jpg',rotX:0,rotY:170},
                {name:'east view',url:'./360s/the_outdoors_360s/east_view.jpg',rotX:0,rotY:50},
                {name:'south view',url:'./360s/the_outdoors_360s/south_view.jpg',rotX:0,rotY:340},
                {name:'north view',url:'./360s/the_outdoors_360s/north_view.jpg',rotX:0,rotY:40},
            ]
        },
    ],
    videos:[    
        '/videos/360_Drone_Reverse.mp4',
    ],
    menu:[
        {
            name:'home'
        },
        {
            name:'entrance',
            list:[
                {name:'lounge'},
                {name:'dinning'},
            ]
        },
        {
            name:'first floor',
            list:[
                {name:'bedroom 1'},
                {name:'bedroom 1'},
                {name:'master bedroom'},
                {name:'balcony'},
            ]
        },
        {
            name:'the outdoors',
            list:[
                {name:'terrace'},
                {name:'west view'},
                {name:'east view'},
                {name:'south view'},
                {name:'north view'},
            ]
        },
    ],
    landingPage:{
        logo:'/elephant_island_logo.png',
        list:[
            {
                name:'individuals',
                btnIcons:{
                    ov:'/individuals_btn_ov.png',
                    off:'/individuals_btn_off.png',
                },
                width:97,
                height:94,
            },
            {
                name:'families',
                btnIcons:{
                    ov:'/families_btn_ov.png',
                    off:'/families_btn_off.png',
                },
                width:128,
                height:94,
            },
            {
                name:'couples',
                btnIcons:{
                    ov:'/couples_btn_ov.png',
                    off:'/couples_btn_off.png',
                },
                width:92,
                height:94,
            },
        ],
        btns:[
            {   
                name:'explore',
                btnIcons:{
                    ov:'/explore_btn_ov.png',
                    off:'/explore_btn_off.png',
                },
                width:120,
                height:46,
            },
            {
                name:'book now',
                btnIcons:{
                    ov:'/book_now_btn_ov.png',
                    off:'/book_now_btn_off.png',
                },
                width:129,
                height:46,
            },
        ],
    },
    menuPoup:{
        home:[
            {
                name:'home',
                btnIcons:{
                    off:'/home_btn_off.png',
                    ov:'/home_btn_ov.png',
                },
                width:97,
                height:94,
            },
        ],
        entrance:[
            {
                location:'entrance',
                name:'entrance',
                btnIcons:{
                    off:'/entrance_btn_off.png',
                    ov:'/entrance_btn_ov.png',
                },
                width:142,
                height:46,
            },
            {
                location:'entrance',
                name:'lounge',
                btnIcons:{
                    off:'/lounge_btn_off.png',
                    ov:'/lounge_btn_ov.png',
                },
                width:119,
                height:39,
            },
            {
                location:'entrance',
                name:'dining',
                btnIcons:{
                    off:'/dining_btn_off.png',
                    ov:'/dining_btn_ov.png',
                },
                width:119,
                height:39,
            },
        ],
        firstFloor:[
            {
                location:'first floor',
                name:'first floor',
                btnIcons:{
                    off:'/first_floor_btn_off.png',
                    ov:'/first_floor_btn_ov.png',
                },
                width:160,
                height:46,
            },
            {
                location:'first floor',
                name:'bedroom 1',
                btnIcons:{
                    off:'/bedroom_1_btn_off.png',
                    ov:'/bedroom_1_btn_ov.png',
                },
                width:149,
                height:39,
            },
            {
                location:'first floor',
                name:'bedroom 2',
                btnIcons:{
                    off:'/bedroom_2_btn_off.png',
                    ov:'/bedroom_2_btn_ov.png',
                },
                width:149,
                height:39,
            },
            {
                location:'first floor',
                name:'master bedroom',
                btnIcons:{
                    off:'/master_bedroom_btn_off.png',
                    ov:'/master_bedroom_btn_ov.png',
                },
                width:204,
                height:39,
            },
            {
                location:'first floor',
                name:'balcony',
                btnIcons:{
                    off:'/balcony_btn_off.png',
                    ov:'/balcony_btn_ov.png',
                },
                width:130,
                height:39,
            },
        ],
        outDoors:[
            {
                location:'the outdoors',
                name:'the outdoors',
                btnIcons:{
                    off:'/the_outdoors_btn_off.png',
                    ov:'/the_outdoors_btn_ov.png',
                },
                width:190,
                height:46,
            },
            {
                location:'the outdoors',
                name:'terrace',
                btnIcons:{
                    off:'/terrace_btn_off.png',
                    ov:'/terrace_btn_ov.png',
                },
                width:128,
                height:39,
            },
            {
                location:'the outdoors',
                name:'west view',
                btnIcons:{
                    off:'/west_view_btn_off.png',
                    ov:'/west_view_btn_ov.png',
                },
                width:150,
                height:39,
            },
            {
                location:'the outdoors',
                name:'east view',
                btnIcons:{
                    off:'/east_view_btn_off.png',
                    ov:'/east_view_btn_ov.png',
                },
                width:150,
                height:39,
            },
            {
                location:'the outdoors',
                name:'south view',
                btnIcons:{
                    off:'/south_view_btn_off.png',
                    ov:'/south_view_btn_ov.png',
                },
                width:150,
                height:39,
            },
            {
                location:'the outdoors',
                name:'north view',
                btnIcons:{
                    off:'/north_view_btn_off.png',
                    ov:'/north_view_btn_ov.png',
                },
                width:159,
                height:39,
            },
        ],
        campOutskirts:[
            {
                location:'the outdoors',
                name:'the outdoors',
                btnIcons:{
                    off:'/camp_site_btn_off.png',
                    ov:'/camp_site_btn_ov.png',
                },
                width:190,
                height:46,
            },
        ],
    },
    menuExp:{
        guide:{
            btnIcons:{
                off:'/guide_btn_off.png',
                ov:'/guide_btn_ov.png',
            },
            width:60,
            height:60,
        },
        upstairs:{
            btnIcons:{
                off:'/upstairs_btn_off.png',
                ov:'/upstairs_btn_ov.png',
            },
            width:60,
            height:60,
        },
        readMore:{
            btnIcons:{
                off:'/read_more_btn_off.png',
                ov:'/read_more_btn_ov.png',
            },
            width:56,
            height:60,
        },
    },
    db360:[
        {
            id: 0,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_house_entrance_001.jpg?alt=media&token=ca965438-9da8-4806-80f4-e2e64b9fbb1a",
            path: "luyari_beta/undefined_house_entrance_001.jpg",
            name: "house_entrance_001"
        },
        {
            id: 1,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_livingroom_001.jpg?alt=media&token=5dbd77ea-a38f-49de-9c9e-70f6f2de6796",
            path: "luyari_beta/undefined_livingroom_001.jpg",
            name: "livingroom_001"
        },
        {
            id: 2,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_dining_001.jpg?alt=media&token=645a393a-f7d0-4521-9411-7b3faac0e177",
            path: "luyari_beta/undefined_dining_001.jpg",
            name: "dining_001"
        },
        {
            id: 3,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_dining_002.jpg?alt=media&token=c1fe6101-fbb6-4628-88f6-9c627a1ef4b7",
            path: "luyari_beta/undefined_dining_002.jpg",
            name: "dining_002"
        },
        {
            id: 4,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_livingroom_002.jpg?alt=media&token=3dae2450-5b04-4e84-b31c-14a99787a84e",
            path: "luyari_beta/undefined_livingroom_002.jpg",
            name: "livingroom_002"
        },
        {
            id: 5,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_First_floor_passage_way.jpg?alt=media&token=180334ef-4ee0-4cbb-bd45-a2cfc967ba5c",
            path: "luyari_beta/undefined_First_floor_passage_way.jpg",
            name: "First_floor_passage_way"
        },
        {
            id: 6,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_bedroom_001.jpg?alt=media&token=9ffbb7a9-dd38-45c1-aca4-240487bf0ddd",
            path: "luyari_beta/undefined_bedroom_001.jpg",
            name: "bedroom_001"
        },
        {
            id: 7,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_bedroom_001v2.jpg?alt=media&token=88755dce-2c3a-4dc7-ae56-026b59a67ce0",
            path: "luyari_beta/undefined_bedroom_001v2.jpg",
            name: "bedroom_001v2"
        },
        {
            id: 8,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_bedroom_002.jpg?alt=media&token=27fc74bc-3cba-478c-881c-0eb9444f7703",
            path: "luyari_beta/undefined_bedroom_002.jpg",
            name: "bedroom_002"
        },
        {
            id: 9,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_master_room_001.jpg?alt=media&token=2a8ab833-2fe6-45db-bce0-11b99b7ff0d0",
            path: "luyari_beta/undefined_master_room_001.jpg",
            name: "master_room_001"
        },
        {
            id: 10,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_master_bathroom_001.jpg?alt=media&token=4211e093-b6fd-4579-b858-241144757583",
            path: "luyari_beta/undefined_master_bathroom_001.jpg",
            name: "master_bathroom_001"
        },
        {
            id: 11,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_master_room_balcony.jpg?alt=media&token=40b9f1af-5c6c-4448-a665-a7e18ba5f1f0",
            path: "luyari_beta/undefined_master_room_balcony.jpg",
            name: "master_room_balcony"
        },
        {
            id: 12,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_001.jpg?alt=media&token=2a950b13-3944-4cdf-a77c-81f3c2ba0f3c",
            path: "luyari_beta/undefined_outdoor_001.jpg",
            name: "outdoor_001"
        },
        {
            id: 13,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_002.jpg?alt=media&token=2a870483-13d6-482f-a4f6-17e9c45b0b91",
            path: "luyari_beta/undefined_outdoor_002.jpg",
            name: "outdoor_002"
        },
        {
            id: 14,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_003.jpg?alt=media&token=b1979905-f652-4ade-8149-47d5364d9196",
            path: "luyari_beta/undefined_outdoor_003.jpg",
            name: "outdoor_003"
        },
        {
            id: 15,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_004.jpg?alt=media&token=d3807258-849a-4e3d-aa95-25d80ed62adc",
            path: "luyari_beta/undefined_outdoor_004.jpg",
            name: "outdoor_004"
        },
        {
            id: 16,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_005.jpg?alt=media&token=1ff3509a-913f-475a-91fc-67d2cd5f52b2",
            path: "luyari_beta/undefined_outdoor_005.jpg",
            name: "outdoor_005"
        },
        {
            id: 17,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_006.jpg?alt=media&token=0a414319-08ad-487d-89bf-730f1bd5ec9b",
            path: "luyari_beta/undefined_outdoor_006.jpg",
            name: "outdoor_006"
        },
        {
            id: 18,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_007.jpg?alt=media&token=aa5f2724-9fa9-4136-a938-5902985333cd",
            path: "luyari_beta/undefined_outdoor_007.jpg",
            name: "outdoor_007"
        },
        {
            id: 19,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_outdoor_008.jpg?alt=media&token=f2f8dc08-b0d5-46e3-b3e4-1dfc33e9b7d6",
            path: "luyari_beta/undefined_outdoor_008.jpg",
            name: "outdoor_008"
        },
        {
            id: 20,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_001.jpg?alt=media&token=cef48cc9-eaee-4761-88de-6e3639ec0d73",
            path: "luyari_beta/undefined_patio_001.jpg",
            name: "patio_001"
        },
        {
            id: 21,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_002.jpg?alt=media&token=c1851d8f-7084-45fc-b726-ea3bce0953a3",
            path: "luyari_beta/undefined_patio_002.jpg",
            name: "patio_002"
        },
        {
            id: 22,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_003.jpg?alt=media&token=433e489b-c391-449f-91e9-349831540212",
            path: "luyari_beta/undefined_patio_003.jpg",
            name: "patio_003"
        },
        {
            id: 23,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_004.jpg?alt=media&token=f7acf42a-d9a0-4d71-8a0a-2a135c67ae46",
            path: "luyari_beta/undefined_patio_004.jpg",
            name: "patio_004"
        },
        {
            id: 24,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_005.jpg?alt=media&token=44c9c646-8202-4af9-8bb6-b5e9d6adb53c",
            path: "luyari_beta/undefined_patio_005.jpg",
            name: "patio_005"
        },
        {
            id: 25,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_006.jpg?alt=media&token=7d0b3ae2-6307-48d1-aefc-95d52cd6296b",
            path: "luyari_beta/undefined_patio_006.jpg",
            name: "patio_006"
        },
        {
            id: 26,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_007.jpg?alt=media&token=2e11be32-3958-4507-b6db-2bb0f50a3265",
            path: "luyari_beta/undefined_patio_007.jpg",
            name: "patio_007"
        },
        {
            id: 27,
            url: "https://firebasestorage.googleapis.com/v0/b/luyari-55dcd.appspot.com/o/luyari_beta%2Fundefined_patio_008.jpg?alt=media&token=01468190-d395-4a15-b4a0-30db46a221d5",
            path: "luyari_beta/undefined_patio_008.jpg",
            name: "patio_008"
        }
    ],
}