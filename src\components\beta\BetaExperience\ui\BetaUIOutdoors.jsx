import React from 'react'
import <PERSON><PERSON><PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUIOutdoors({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
   const habdleMasterBed = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[2].name})
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[2].list[4].name})
      }
  
      const handleInfo = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
      }
  
      const handleVideo = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'video'})
      }
  return (
    <>
         {/* BEDROOM */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[40,-3,40]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={habdleMasterBed}/>
        </Html>
         {/* BEDROOM */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[3,-10,40]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.readMore.height,width:settings.menuExp.readMore.width,image:settings.menuExp.readMore.btnIcons}} handleClick={handleInfo}/>
        </Html>
         {/* LEFT LAMP */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-8,7,-60]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.infoVideo.height,width:settings.menuExp.infoVideo.width,image:settings.menuExp.infoVideo.btnIcons}} handleClick={handleVideo}/>
        </Html>
    </>
  )
}
