import { settings } from '@/libs/betasiteSettings';
import Link from 'next/link';
import LogoComponent from '../LogoComponent';
import BetaBtnMenu from './BetaBtnMenu';
import BetaBookNowBtn from './BetaBookNowBtn';

export default function BetaNavbarComponent() {
  return (
    <nav className="flex fixed z-30 top-0 left-0 w-full h-[75px] items-start from-black justify-between bg-gradient-to-b text-white">
      <Link href={'/'} className="flex bg-inherit object-left-top relative w-fit h-fit text-lg tracking-[6px]">
        <LogoComponent/>
        {/* {settings.siteName} */}
      </Link>
      <div className='flex items-center h-full w-fit'>
        {/* <Link className='br-10' href={'/dashboard'}>
          <span className='flex capitalize items-center justify-center text-sm'>dashboard</span>
        </Link> */}
        <div className='flex w-24 h-full justify-center items-center'>
          <BetaBtnMenu/>
        </div>
        <div className='flex w-24 h-full justify-center items-center'>
          <BetaBookNowBtn/>
        </div>
      </div>
    </nav>
  )
}
