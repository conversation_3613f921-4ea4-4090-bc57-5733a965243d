import ExperiencePOILandingPage from '@/components/experience/ExperiencePOILandingPage'
import ExperiencePOIpositionMarker from '@/components/experience/ExperiencePOIpositionMarker'
import { settings } from '@/libs/betasiteSettings'
import { useDashboardContext } from '@/libs/contextProviders/useContextDashboard'
import { Html } from '@react-three/drei'
import { useControls } from 'leva'
import { usePathname, useRouter } from 'next/navigation'
import React, { useEffect, useMemo, useRef } from 'react'
import { IoInformation } from 'react-icons/io5'
import { BackSide } from 'three'
import { degToRad } from 'three/src/math/MathUtils'


function POIObject({data,handleClick}) {
  const {
    markerObject,setMarkerObject,
    markerList,setMarkerList,
    inputList,setInputList
  }=useDashboardContext()

  const refObject=useRef()
  const pathname=usePathname()

  const options = useMemo(() => {
    return {
      x: { value: 0, min: -200, max: 200, step: 0.001 },
      y: { value: 0, min: -200, max: 200, step: 0.001 },
      z: { value: -200, min: -200, max: 200, step: 0.001 },
    }
  }, [])
      
  const position = useControls(data?.name, options)  
  
  // Update the marker object
  useEffect(() => {   
    // console.log('useEffect:',markerList?.find(({name})=>name===data?.name))
    setMarkerObject({...markerObject,position:Object.values(position)})
  }, [position,data?.markerType])
  
  // console.log('useEffect markerObject:',markerObject)   

  return(
    <Html
      center
      position={[position.x,position.y,position.z]}
    >
      {
        data?.markerType=="landingPage" ? <ExperiencePOILandingPage handleClick={handleClick}/> :
        data?.markerType=="positionMarker" ? <ExperiencePOIpositionMarker handleClick={handleClick} data={settings.menuExpObjects.positionMarker}/> :
        data?.markerType=="levelMarker" ? <ExperiencePOIpositionMarker handleClick={handleClick} data={settings.menuExpObjects.levelMarker}/> :
        data?.markerType=="infoDoc" ? <ExperiencePOIpositionMarker handleClick={handleClick} data={settings.menuExpObjects.infoDoc}/> :
        data?.markerType=="infoImage" ? <ExperiencePOIpositionMarker handleClick={handleClick} data={settings.menuExpObjects.infoImage}/> :
        data?.markerType=="infoVideo" ? <ExperiencePOIpositionMarker handleClick={handleClick} data={settings.menuExpObjects.infoVideo}/> :
        <IoInformation className='flex text-5xl p-2 rounded-full bg-white'/>
      }
    </Html>
  )
}

export default function DashboardExperience360({texture}) {
    const {
      dashboardStateEdit,dashboardDispatchEdit,
      dataObject,setDataObject,
      markerList,setMarkerList,
      refresh360,
      inputList,setInputList
    }=useDashboardContext()

    const refObject=useRef()
    const refmaterial=useRef()
    const router=useRouter()

    const handleDoubleClick = () => {
      // console.log(dataObject?.name)
      router.push(`/dashboard/${dataObject?._id}`)
    }
    
    const handleClick = (params) => {
      
    }
    
    const options = useMemo(() => {
      return {
        y: { value: dataObject._360Rotation!=0 ? dataObject._360Rotation : 0, min: -0, max: 360, step: 0.001 },
      }
    }, [])
    
    const rotation = useControls('360 rotate', options)

    useEffect(() => {
      dataObject._360Rotation==0 && setDataObject({...dataObject,_360Rotation:rotation.y})
    }, [rotation.y])

    // console.log('DashboardExperience360 markerList:',markerList)

  return (
    <>
      {/* 360 Sphere */}
      {<mesh
        onDoubleClick={e=>handleDoubleClick()}
        ref={refObject}
        name='360Sphere'
        scale={[1,1,-1]}
        rotation={[0,degToRad(rotation.y),0]}
      >
        <sphereGeometry
            args={[128,128,128]}
        />
        <meshBasicMaterial
          ref={refmaterial}
          map={texture} 
          side={BackSide}
        />
      </mesh>}

      {/* Marker list */}
      {markerList?.map((i,index)=> 
        <POIObject handleClick={handleClick} data={i} key={index}/>
      )}
    </>
  )
}
