import DashboardWrapper from "@/components/dashboard/DashboardWrapper";
import { settings } from "@/libs/siteSettings";

export default async function DashboardPage() {
  // console.log('Home:',)
  const data=settings._360sDb
  // const dataBd = await fetch(`${settings.url}/api/360s`)
  // const data = await dataBd.json()
  // console.log('DashboardPage:',data)

  return (
    <div className="flex w-full h-full bg-gray-400/50 mb-5 rounded-md shadow-md overflow-hidden">
      <DashboardWrapper data={data}/>
    </div>
  );
}
export const dynamic = 'force-dynamic'