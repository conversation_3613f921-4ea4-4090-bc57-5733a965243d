import { settings } from '@/libs/siteSettings'
import { Html } from '@react-three/drei'
import Image from 'next/image'
import React, { useState } from 'react'

export default function ExperiencePOILevelMarker({data,handleClick}) {
  const [swap,setSwap]=useState(true)
  console.log('ExperiencePOILevelMarker:',data)
  return (
    <div 
      onMouseEnter={()=>setSwap(!swap)} 
      onMouseLeave={()=>setSwap(!swap)} 
      onClick={handleClick}
      className='btn flex relative items-center select-none justify-center cursor-pointer max-w-fit max-h-fit'
    >
      <div 
        // onClick={handleClick?.[index]} 
        className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
      >
        <Image className='flex-none' width={settings.menuExpObjects.levelMarker.width} height={settings.menuExpObjects.levelMarker.height} alt='button images for landpage options' src={settings.menuExpObjects.levelMarker.btnIcons?.ov}/>
      </div>
      <div
          // onClick={handleClick?.[index]} 
          className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
      >
        <Image className='flex-none' width={settings.menuExpObjects.levelMarker.width} height={settings.menuExpObjects.levelMarker.height} alt='button images for landpage options' src={settings.menuExpObjects.levelMarker.btnIcons?.off}/>
      </div>
    </div>
  )
}