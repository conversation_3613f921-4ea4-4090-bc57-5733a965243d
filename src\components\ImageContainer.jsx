'use client'
import Image from 'next/image'
import React, { useEffect, useRef, useState } from 'react'



export default function ImageContainer({data,brightness,alt,className,style}) {
  const [onImageLoad,setOnImageLoad]=useState(false)
  const imageLoaded = (params) => {
    setOnImageLoad(true)
  }
  
  // console.log('ImageContainer :-',data)
  return (
    <div className={`flex w-full h-full relative select-none flex-none ${brightness,style}`}>
      {data && <Image 
        src={data} 
        alt={alt ? alt : "hero images" }
        fill 
        className={`transition-opacity ${className} object-cover duration-[2s] ${onImageLoad ? 'opacity-100' : 'opacity-0'}`} 
        onLoad={imageLoaded}
      />}
    </div>
  )
}