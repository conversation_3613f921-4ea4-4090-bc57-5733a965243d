'use client'
import React, { useEffect } from 'react'
import dynamic from 'next/dynamic'
import { useDashboardContextComponent } from '@/libs/contextProviders/useDashboardContextComponent'
import TestInputWrapper from './testinput/TestInputWrapper'

const TestWorld = dynamic(() => import('./TestWorld'), { ssr: false })

export default function TestWrapper({data,dataList}) {
  const {dataArrayList,setDataArrayList,dbData,setDbData,_360ArrayList,set_360ArrayList}=useDashboardContextComponent()

  // make available the list from the db
  useEffect(() => {
    // console.log('testwrapper loaded:',data instanceof Array)
    data?.length>1 ? setDataArrayList(data) : setDataArrayList([data])
    data && setDbData(true)
    dataList && set_360ArrayList(dataList)
  }, [data,dataList])

  // console.log('TestWrapper:',dataList)
  return (
    <div className='flex w-full h-full'>
      <TestInputWrapper>
        <TestWorld/>
      </TestInputWrapper>
    </div>
  )
}
