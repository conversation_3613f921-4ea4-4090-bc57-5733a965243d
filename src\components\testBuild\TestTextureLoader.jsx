import React, { useEffect, useRef, useState } from 'react'
import Test360 from './Test360'
import { LoadingManager, TextureLoader } from 'three'
import { useTextureLoader } from '@/libs/useTextureLoader'
import { useDashboardContextComponent } from '@/libs/contextProviders/useDashboardContextComponent'
import { Html } from '@react-three/drei'
import TextureLoaderSpiner from './TextureLoaderSpiner'
import { usePathname } from 'next/navigation'

export default function TestTextureLoader() {
    const {
        dataArrayList,setDataArrayList,
        textureId,setTextureId,
        textureArray,setTextureArray,
        textureObject,setTextureObject,
        texturesLoaded,setTexturesLoaded,
        tag,setTag
    }=useDashboardContextComponent()

    // const [textureArray,setTextureArray]=useState([])
    const pathName=usePathname()
    const [refresh,setRefresh]=useState(false)
    const refLoaderManger=useRef(new LoadingManager())   
    const refLoader=useRef(new TextureLoader(refLoaderManger.current))   

    useEffect(() => {
        // console.log('TestTextureLoader:',dataArrayList)

        if(dataArrayList){
            const textArrayList=useTextureLoader(dataArrayList,refLoader.current)
            setTextureArray(textArrayList)
    
            refLoaderManger.current.onStart=(url,itemsLoaded,itemsTotal)=>{
                // console.log(`started loading file:${url}, ${itemsLoaded} of ${itemsTotal}`)
            }
    
            refLoaderManger.current.onLoad=()=>{
                console.log(`files loaded`)
                setRefresh(!refresh)
                setTexturesLoaded(true)
            }
            
            refLoaderManger.current.onProgress=(url,itemsLoaded,itemsTotal)=>{
                // console.log(`loading file: ${url}, ${itemsLoaded} of ${itemsTotal}`)
                setRefresh(!refresh)
                setTag(`${itemsLoaded} of ${itemsTotal} 360s loaded`)
            }
    
            refLoaderManger.current.onError=(url)=>{
                // console.log(`error loading file: ${url}`)
            }
        }

        return ()=>{
            // console.log('TestTextureLoader unloaded:')
            textureArray?.map((item)=>{
                item.dispose()
            })
        }
    }, [dataArrayList?.length,dataArrayList]) 
    
    // textureArray?.length-1==dataArrayList?.length-1 && dataArrayList?.map((i,index)=>{
    textureArray && textureArray?.map((i,index)=>{
        const firtIndex=textureArray[index]?.source?.data?.currentSrc?.indexOf('Newfolder/')
        const lastIndex=textureArray[index]?.source?.data?.currentSrc?.lastIndexOf('.')
        const word=textureArray[index]?.source?.data?.currentSrc?.slice(firtIndex+10,lastIndex)
        textureArray[index].name=word
        // console.log(word)
        
        // textureArray[index].name=i.name
    })

    useEffect(() => {
        textureArray?.length>0 && setTextureObject(textureArray?.find(({name})=>name==textureId))
    }, [textureId,textureArray?.length>0])

    if(!texturesLoaded) return <Html center><TextureLoaderSpiner/></Html>
   
    // console.log('TestTextureLoader textureArray:',texturesLoaded)

  return (
    <>
      <Test360 texture={pathName.split('/').length-1<2 && textureId ? textureArray?.find(({name})=>name==textureId) : textureArray[0]}/>
    </>
  )
}
