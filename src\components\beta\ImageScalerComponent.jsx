'use client';
import React, { useRef, useState, useEffect } from 'react';

export default function ImageScalerComponent({
    src,
    alt,
    style,
    onLoad,
    onError,
    ...props
  }) {
    const [isClient, setIsClient] = useState(false);
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);
    const [sizes, setSizes] = useState({ width: 0, height: 0 });
    const containerRef = useRef(null);
    const imageRef = useRef(null);

    useEffect(() => {
      setIsClient(true)
    },[])

    useEffect(() => {
        if (!isClient) return; // Skip if not client-side yet

        setIsLoaded(false)
        const img = new Image()
        img.src = src

        img.onload = () => {
            setIsLoaded(true)
            setSizes({width: img.width, height: img.height})
        }

        img.onerror = () => {
            setHasError(true)
            setIsLoaded(true) // Still mark as loaded to avoid infinite loading state

        }

        return () => {
            // Cleanup
            img.onload = null
            img.onerror = null
        }
    }, [src, isClient])

    if(!isClient) {return null}

    // console.log(isClient)
  return (
    <div
      ref={containerRef}
      style={{width:sizes.width,height:sizes.height}}
      className={`flex items-center justify-center ${style} ${isLoaded ? 'opacity-100' : 'opacity-0'} ${hasError ? 'bg-red-100' : ''} transition-[opacity] w-[${sizes.width}px] h-[${sizes.height}px}] duration-300 ease-in-out`}
    >
      <img
        ref={imageRef}
        src={src}
        alt={alt}
        className={`${style ? style : 'object-contain'} w-full h-full`}
        onLoad={onLoad}
        onError={(e) => {
          setHasError(true);
          if (onError) onError(e);
        }}
        {...props}
      />
    </div>
  )
}
