import React from 'react'

const Spinner = ({ size = 'h-6 w-6', color = 'text-blue-500', borderWidth = 'border-2', className = '' }) => {
  return (
    <div className={`animate-spin rounded-full ${borderWidth} border-t-transparent ${color} ${size} ${className}`}></div>
  );
};

// function name(params) {
  
// }

export default function BetaLoadingComponent() {
  return (
    <div className='flex absolute z-10 top-0 left-0 text-white w-fit h-fit items-center justify-center gap-2'>
      <div className="flex items-center justify-center gap-4">
        <Spinner size="h-10 w-10" color="text-white" borderWidth="border-4" className="items-center justify-center" />
        {/* <p className="text-gray-50">Loading...</p> */}
      </div>
    </div>
  )
}
