'use client'

import { useEffect, useState } from "react"
import Experience from "../BetaExperience/BetaExperience"
import DashboardInputComponent from "./DashboardInputComponent"
import { useExperienceContext } from "@/libs/contextProviders/useBetaContextExperience"

export default function DashboardProjectInputWrapper({data,inputfields}) {
    const {userInfoDetails,setUserInfoDetails,fileArray,setFileArray,dataExperience,setDataExperience}=useExperienceContext()
    
    // console.log('DashboardProjectInputWrapper:',inputfields)
  return (
    <div className='flex relative flex-col max-h-full w-11/12 md:w-1/2 md:h-4/5 h-full py-2 md:py-0 items-center justify-center gap-5'>
        <div className='flex relative w-full h-2/3 bg-slate-100 overflow-hidden rounded-md'>
            <Experience/>
        </div>
        <div className='flex relative w-full h-1/3 rounded-md items-center justify-center'>
            <DashboardInputComponent data={data} input={inputfields}/>
        </div>
    </div>
  )
}
