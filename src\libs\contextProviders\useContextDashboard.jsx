'use client'

import { createContext, useContext, useReducer, useState } from "react"
import { INITIAL_DASHBOARD_STATE, reducerDashboard } from "./reducerDashboard"

const DashboardContext=createContext()

export default function DashboardContextProvider({children}) {
    const [dashboardStateEdit,dashboardDispatchEdit]=useReducer(reducerDashboard,INITIAL_DASHBOARD_STATE)
    const [experienceStateList,setExperienceStateList]=useState([])
    const [webGlCompatibilty,setWebGlCompatibilty]=useState(true)
    const [dataObject,setDataObject]=useState({cameraPosition:0,_360Rotation:0,markerList:[]})
    const [dataUpdate,setDataUpdate]=useState([])
    const [markerList,setMarkerList]=useState([])
    const [markerObject,setMarkerObject]=useState({position:[0,0,0]})
    const [inputList,setInputList]=useState([])
    const [locationList,setLocationList]=useState([])
    const [searchInput,setSearchInput]=useState()
    const [textureId,setTextureId]=useState('entrance')
    const [refresh360,setRefresh360]=useState(false)
    const [texturesLoaded,setTexturesLoaded]=useState(false)
    
    return (
        <DashboardContext.Provider
            value={{
                dashboardStateEdit,dashboardDispatchEdit,
                experienceStateList,setExperienceStateList,
                webGlCompatibilty,setWebGlCompatibilty,
                dataObject,setDataObject,
                dataUpdate,setDataUpdate,
                markerObject,setMarkerObject,
                searchInput,setSearchInput,
                inputList,setInputList,
                locationList,setLocationList,
                refresh360,setRefresh360,
                textureId,setTextureId,
                texturesLoaded,setTexturesLoaded,
                markerList,setMarkerList
            }}
        >
            {children}
        </DashboardContext.Provider>
    )
}

export const useDashboardContext=()=>{
    const context=useContext(DashboardContext)
    if(!context){
        throw new Error('useDashboardContext must be used within an ExperienceProvider')
    }
    return context
}