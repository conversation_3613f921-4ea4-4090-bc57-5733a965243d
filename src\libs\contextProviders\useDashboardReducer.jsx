export const INITIAL_DASHBOARD_STATE={
    
}

export const DASHBOARD_ACTION={
    RESET:'RESET'
}

export const useDashboardReducer=(state,action)=> {
    switch (action.type) {
        case 'MENU_TYPE':
            return{
                ...state,
            }     
        case 'RESET':
            return{
                ...state,
            }     
        default:
            state
        break;
    }
}

