'use client'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import React from 'react'

export default function DashboardAddButton() {
    const pathname=usePathname()
    const router=useRouter()
    // console.log('DashboardAddButton:',pathname?.split('/'))
  return (
    <>
      {pathname?.split('/')?.length-1>1
        ? <button onClick={()=>router.back()} className='flex bg-gray-900 round shadow-lg text-white uppercase p-3 px-6 rounded-md absolute bottom-6 right-6 z-10' href={`/${pathname?.split('/')[1]}/add`}>back</button>
        :<Link className='flex bg-gray-900 round shadow-lg text-white uppercase p-3 px-6 rounded-md absolute bottom-6 right-6 z-10' href={`/${pathname?.split('/')[1]}/add`}>
          add project
        </Link>
      }
    </>
  )
}
