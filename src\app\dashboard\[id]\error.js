'use client' // Error boundaries must be Client Components
 
import ErrorComponent from '@/components/ErrorComponent'
import Link from 'next/link'
import { useEffect } from 'react'
 
export default function Error({ error, reset }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error)
  }, [error])
 
  return (
    <div className='flex items-center justify-center w-full h-full'>
      <ErrorComponent reset={reset}/>
    </div>
  )
}