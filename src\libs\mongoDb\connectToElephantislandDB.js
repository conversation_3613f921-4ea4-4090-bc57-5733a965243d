import mongoose from "mongoose"
export const connectToElephantislandDB = async () => {
    try {
        let db={}
        if(db.readyState==1){
            console.log('elephandisland mongoDb already connected')
        }else{
            db=mongoose.connection.on('elephandisland connected', () => console.log('elephandisland to luyari mongoDb'))
            await mongoose.connect(process.env.MONGO_DB_ELEPHANTISLAND)
        }
    } catch (error) {
        mongoose.connection.on('error',err=>{console.log(err)})
        mongoose.connection.on('error',err=>{logError(err),console.log(err)})
        handleError(error);
        mongoose.connection.close()
    }
}
