import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import Image from 'next/image'
import React, { useState } from 'react'

export default function BetaMenuMessage() {
    const {experienceState,experienceDispatch}=useBetaContextExperience()

    const [showPopup,setShowPopup]=useState(false)
    
    // console.log('BetaMenuMessage:',experienceState?.menuType)
  return (
    <>
        {experienceState?.menuType=='info'
            // ?   <div className='flex flex-col absolute top-0 left-0 max-h-fit min-w-fit overflow-hidden'>
            ?   <div className='flex z-50 flex-col absolute top-[75px] left-0 max-h-fit min-w-fit p-[75px] overflow-hidden'>
                    <div className='flex w-full h-auto overflow-hidden'>
                        <Image className='image object-left' src={'/lounge_info_popup.png'} alt='background image' width={1016} height={719}/>
                    </div>
                </div>  :
            experienceState?.menuType=='booknow'
                ?   <>
                        <div className='flex z-50 flex-col absolute top-[75px] left-0 max-h-fit min-w-fit p-[75px] overflow-hidden'>
                            <div className='flex w-full h-auto'>
                                <Image className='image object-left' src={'/booking_form_001.png'} alt='background image' width={891} height={600}/>
                            </div>
                        </div>
                    </> : 
            experienceState?.menuType=='video'
                ?   <>
                        <div onClick={e=>setShowPopup(!showPopup)} className='flex z-50 flex-col absolute top-0 left-0 h-full w-full overflow-hidden'>
                            <div className='flex mt-[75px] p-[75px] w-full h-auto  overflow-y-auto overflow-hidden'>
                                <Image className='image object-left' src={'/video_thumbnails.png'} alt='background image' width={891} height={600}/>
                            </div>
                        </div>
                        {showPopup && <div onClick={e=>setShowPopup(!showPopup)} className='flex z-50 flex-col absolute top-0 left-0 h-full w-full overflow-hidden bg-black/75'>
                            <div className='flex mt-[75px] p-[75px] w-full h-auto '>
                                <Image className='image object-left' src={'/video_display.png'} alt='background image' width={871} height={730}/>
                            </div>
                        </div>}
                    </> : 
            experienceState?.menuType=='gallery'
                ?   <>
                        <div onClick={e=>setShowPopup(!showPopup)} className='flex z-50 flex-col absolute top-0 left-0 h-full w-full overflow-hidden'>
                            <div className='flex flex-col mt-[75px] p-[75px] w-full h-auto overflow-y-auto overflow-hidden'>
                                <Image className='image object-left' src={'/gallery_001.png'} alt='background image' width={891} height={600}/>
                                <Image className='image object-left' src={'/art_panel_002.png'} alt='background image' width={891} height={600}/>
                                <Image className='image object-left' src={'/art_panel_003.png'} alt='background image' width={891} height={600}/>
                            </div>
                        </div>
                        {showPopup && <div onClick={e=>setShowPopup(!showPopup)} className='flex z-50 flex-col absolute top-0 left-0 h-full w-full overflow-hidden bg-black/85'>
                            <div className='flex mt-[75px] p-[75px] w-full h-auto '>
                                <Image className='image object-left' src={'/art_piece_popup.png'} alt='background image' width={871} height={730}/>
                            </div>
                            {/* <div className='flex mt-[75px] p-[75px] w-full h-auto overflow-auto'>
                                <Image className='image object-left' src={'/art_piece_popup.png'} alt='background image' width={1114} height={1035}/>
                            </div> */}
                        </div>}
                    </> : null
        }
    </>
  )
}
