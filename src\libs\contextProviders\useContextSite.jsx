'use client'
import { createContext, useContext, useReducer, useState } from "react"
import { INITIAL_EXPERIENCE_STATE, reducerExperience } from "./reducerExperience"

export const SiteContext=createContext()

export default function SiteContextProvider({children}) {
    const [siteState,siteDispatch]=useReducer(reducerExperience,INITIAL_EXPERIENCE_STATE)
    return (
        <SiteContext.Provider
            value={{siteState,siteDispatch}}
        >
            {children}
        </SiteContext.Provider>
    )
}

export const useSiteContext=()=>{
    return useContext(SiteContext)
}