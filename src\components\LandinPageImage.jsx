  'use client'

import { useExperienceContext } from "@/libs/contextProviders/useExperienceContext"
import Image from "next/image"
import { useEffect } from "react"
import ImageContainer from "./ImageContainer"
import LandingpageComponent from "./LandingpageComponent"

function ImagesDownloadComponent({data}) {
  return(
    <div className='flex relative w-full min-h-32 max-h-36 bg-gray-900 overflow-hidden cursor-pointer rounded-md'>
      <Image priority className='flex brightness-75 scale-100 hover:scale-105 duration-200 ease-linear' src={data?.url} alt='360 image' fill/>
    </div>
  )
}

export default function LandinPageImage({data}) {
  const {
    experienceState,experienceDispatch,
    dataUpdate,setDataUpdate,
  }=useExperienceContext()

  useEffect(() => {
    setDataUpdate(data)
  }, [data])
  
  // console.log('LandinPageImage:',dataUpdate)
  return (
    <div className='z-40 landinPageImage flex w-full h-full fixed top-0 left-0 bg-black text-white'>
      <div className="flex w-full h-full relative">
        <ImageContainer data={'/hero_image_001.jpg'}/>
        <LandingpageComponent/>
      </div>
    </div>
  )
}
