import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import ExperienceContextProvider from "@/libs/contextProviders/useExperienceContext";
import { settings } from "@/libs/siteSettings";
import BetaExperienceContextProvider from "@/libs/contextProviders/useBetaContextExperience";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: settings.siteName.name,
  description: settings.siteName.maxim,
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <BetaExperienceContextProvider>
        <ExperienceContextProvider>
          <div className="flex bg-black h-screen w-screen">
            {children}
          </div>
        </ExperienceContextProvider>
        </BetaExperienceContextProvider>
      </body>
    </html>
  );
}
