import React from 'react'
import <PERSON><PERSON><PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import BetaExperienceStairsPointComp from './BetaExperienceStairsPointComp'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUIStairsComp({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
   const handleLounge = (name) => {
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[1].name})
    }
   const handleBedroom1 = (name) => {
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[1].name})
    }
   const handleBedroom2 = (name) => {
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[2].name})
    }
   const handleMasterBed = (name) => {
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[3].name})
    }
   const handleGallery = (name) => {
      experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'gallery'})
    }
  return (
    <>
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[23,-25,-20]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.upstairsReverse.height,width:settings.menuExp.upstairsReverse.width,image:settings.menuExp.upstairsReverse.btnIcons}} handleClick={handleLounge}/>
        </Html>
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-2,-2,-20]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleBedroom1}/>
        </Html>
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-24.5,-3,-20]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleBedroom2}/>
        </Html>
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[13,-3,20]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleMasterBed}/>
        </Html>
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[100,-20,2]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.infoGallery.height,width:settings.menuExp.infoGallery.width,image:settings.menuExp.infoGallery.btnIcons}} handleClick={handleGallery}/>
        </Html>
    </>
  )
}
