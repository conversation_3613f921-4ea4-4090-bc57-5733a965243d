'use client'

import { Canvas } from "@react-three/fiber"
import { Suspense, useEffect, useState } from "react"
import ExperienceTextureLoader from "./experienceComponents/ExperienceTextureLoader"
import ExperienceControls from "./experienceComponents/ExperienceControls"
import ExperienceLoader from "./experienceComponents/ExperienceLoader"
import { useExperienceContext } from "@/libs/contextProviders/useExperienceContext"
import LoadingComponent from "../LoadingComponent"

export default function ExperienceWorld({data}) {
    const {
      experienceState,experienceDispatch,
      dataUpdate,setDataUpdate,
      dataObject,setDataObject,
    }=useExperienceContext()

    const [scrollIndex,setScrollIndex]=useState(0)
    const [showLoader,setShowLoader]=useState(false)
    

    useEffect(() => {
      // console.log('ExperienceWorld loaded:',)
      setDataUpdate(data)
      // data?.length>0 && setDataObject(data?.find(i=>i?.name===experienceState?._360Name))
      data?.length>0 && setDataObject(data[scrollIndex])
      return () => {
        // console.log('ExperienceWorld unloaded:',)
      }
    }, [data,scrollIndex])

    // console.log('ExperienceWorld:',dataObject)
  return (
    <div className="experienceWorld flex relative w-full h-full items-center justify-center">
      <button onClick={e=>setScrollIndex(dataUpdate?.length-1==scrollIndex ? 0 : scrollIndex+1)} className="flex absolute top-4 left-4 items-center justify-center w-fit h-fit p-2 px-5 bg-gray-900 text-white border-2 border-gray-50 capitalize cursor-pointer rounded-md shadow z-20">next</button>
      <Canvas>
        <Suspense fallback={<ExperienceLoader/>}>
          <ExperienceTextureLoader setShowLoader={setShowLoader}/>
          <ExperienceControls/>
        </Suspense>
      </Canvas>
    </div>
  )
}
