import VideoWrapper from '@/components/VideoWrapper'
import { settings } from '@/libs/siteSettings'
import Link from 'next/link'
import React from 'react'

export default function HeroPage() {
  const data=settings.videos
  // console.log('HeroPage:',data)
  return (
    <div className={`videoWrapper flex videoWrapper sm:h-dvh h-full w-full items-center justify-center`}>
      <VideoWrapper data={data}/>
    </div>
  )
}
