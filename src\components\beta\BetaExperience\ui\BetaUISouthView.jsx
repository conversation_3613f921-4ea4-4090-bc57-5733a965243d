import React from 'react'
import BetaE<PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUISouthView({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
   const habdleOutdoor_008 = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[0].name})
      }
      console.log(settings._360s[2].name)
      console.log(settings._360s[2].list[2].name)
  return (
    <>
      {/* LEFT LAMP */}
      <Html
        className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
        position={[-10,7,-60]}
        occlude
      >
        <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={habdleOutdoor_008}/>
      </Html>
    </>
  )
}
