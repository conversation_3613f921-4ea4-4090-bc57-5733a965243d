'use client'
import React, { createContext, useContext, useReducer, useState } from 'react'
import { INITIAL_DASHBOARD_STATE, useDashboardReducer } from './useDashboardReducer'

export const DashboardContext=createContext()

export default function DashboardContextProvider({children}) {
    const [DashboardState,DashboardDispatch]=useReducer(useDashboardReducer,INITIAL_DASHBOARD_STATE)
    const [dataArrayList,setDataArrayList]=useState([])
    const [_360ArrayList,set_360ArrayList]=useState([])
    const [markerList,setMarkerList]=useState([])
    const [markerObject,setMarkerObject]=useState({})
    const [locationList,setLocationList]=useState([])
    const [textureArray,setTextureArray]=useState([])
    const [textureId,setTextureId]=useState('')
    const [searchInput,setSearchInput]=useState('')
    const [texturesLoaded,setTexturesLoaded]=useState(false)
    const [refresh,setRefresh]=useState(false)
    const [dbData,setDbData]=useState(false)
    const [textureObject,setTextureObject]=useState({})
    const [dataObject,setDataObject]=useState({cameraPosition:0,_360Rotation:0})
    const [tag,setTag]=useState('')
    
    return(
        <DashboardContext.Provider value={{
            DashboardState,DashboardDispatch,
            searchInput,setSearchInput,
            textureId,setTextureId,
            locationList,setLocationList,
            textureArray,setTextureArray,
            texturesLoaded,setTexturesLoaded,
            textureObject,setTextureObject,
            dataObject,setDataObject,
            refresh,setRefresh,
            markerList,setMarkerList,
            markerObject,setMarkerObject,
            tag,setTag,
            _360ArrayList,set_360ArrayList,
            dbData,setDbData,
            dataArrayList,setDataArrayList
        }}>
            {children}
        </DashboardContext.Provider>
    )
}

export function useDashboardContextComponent() {
    const context=useContext(DashboardContext)
    if(!context){
        throw new Error('useDashboardContext must be used within an ExperienceProvider')
    }
    return context
}
