import { ref, uploadBytesResumable, getDownloadURL, deleteObject } from "firebase/storage";
import { storage } from "./firebase";

export const uploadFiles = (name,extension,file,setProgress,setFileArray) => {
    // console.log('upload fn -',projectTitle,name,extension,file,setProgress,setFileArray)
    // const storage = getStorage();
    // console.log(inputArgs)
    const path=`elephantisland/_360_${name}.${extension}`
    let objsArray=[]

    // console.log(file)
    // console.log(path,name,extension)

    const storageRef = ref(storage, path);
    
    const uploadTask = uploadBytesResumable(storageRef, file);
    
    // Register three observers:
    // 1. 'state_changed' observer, called any time the state changes
    // 2. Error observer, called on failure
    // 3. Completion observer, called on successful completion
    uploadTask.on('state_changed', 
      (snapshot) => {
        // Observe state change events such as progress, pause, and resume
        // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        setProgress(progress)
        // console.log('Upload is ' + progress + '% done');
        switch (snapshot.state) {
          case 'paused':
            // console.log('Upload is paused');
            break;
          case 'running':
            // console.log('Upload is running');
            break;
        }
      }, 
      (error) => {
        // Handle unsuccessful uploads
      }, 
      () => {
        // Handle successful uploads on complete
        // For instance, get the download URL: https://firebasestorage.googleapis.com/...
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          // objsArray.push()
          setFileArray(prev=>[...prev,{id:prev?.length,url:downloadURL,path:path,name:name}])
          // setUserInfoDetails(prev=>prev={...prev,[inputArgs?.name]:file})
          // console.log('image uploaded',downloadURL)
        });
        // console.log('end of code',{[inputArgs]:fileArray},[inputArgs])
      }
    );
}

export const deleteFiles = (path) => {
  
    // const storage = getStorage();
    
    // Create a reference to the file to delete
    const desertRef = ref(storage, path);
    
    // Delete the file
    deleteObject(desertRef).then(() => {
      // File deleted successfully
      // console.log(`${path} File deleted successfully`)
    }).catch((error) => {
      // Uh-oh, an error occurred!
    });
}

export const getTextureURL = async (storagePath) => {
  try {
    const storageRef = ref(storage, storagePath);
    const url = await getDownloadURL(storageRef);
    return url;
  } catch (error) {
    console.error("Error getting download URL:", error);
    return null;
  }
};
