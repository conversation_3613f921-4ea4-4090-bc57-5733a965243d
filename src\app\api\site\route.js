import { connectToElephantislandDB } from "@/libs/mongoDb/connectToElephantislandDB";
import { NextResponse } from "next/server";
import { Site } from "@/libs/mongoDb/models/Site";

export async function GET(req) {
    connectToElephantislandDB()
    try {
        const site=await Site.find()
        return NextResponse.json(site,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get site',{status:500})
    }
}

export async function POST(req) {
    const body=await req.json()
    // console.log('site api route',body)
    connectToElephantislandDB()
    try {
        // const site=await Site.findOne({projectTitle:body?.projectTitle})
        // if(foundProejct) return NextResponse.json('site details already exists',{status:501})
        const site=await Site(body)
        site.save()
        return NextResponse.json(site,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get site details',{status:500})
    }
}

export async function PATCH(req) {
    const body=await req.json()
    console.log('site api route',body)
    connectToElephantislandDB()
    try {
        const foundProejct=await Site.findOne({projectTitle:body?.projectTitle})
        if(foundProejct) return NextResponse.json('site details already exists',{status:501})
        const site=await Project(body)
        newProject.save()
        return NextResponse.json(site,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get site details',{status:500})
    }
}