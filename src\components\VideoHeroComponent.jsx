import { settings } from '@/libs/siteSettings';
import React, { useEffect, useRef, useState } from 'react'
import LoadingComponent from './LoadingComponent';
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience';
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience';

export default function VideoHeroComponent({data}) {
  const videoRef=useRef(null)
  const {experienceState,experienceDispatch}=useBetaContextExperience()

  const [showSpinner,setShowSpinner]=useState(false)
  
  const handleClick = () => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.SHOW_360})
  }
  
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(error => {
        console.log('Video autoplay prevented:', error)
      });
    }
  }, [])

  // console.log('VideoHeroComponent:',videoRef)
  return (
    <div className='relative h-screen w-full items-center justify-center overflow-hidden'>
      {showSpinner && <LoadingComponent/>}
      <video
        ref={videoRef}
        autoPlay
        muted
        onEnded={handleClick}
        onWaiting={e=>setShowSpinner(true)}
        onPlaying={e=>setShowSpinner(false)}
        onLoadedData={e=>setShowSpinner(false)}
        playsInline
        className="h-full w-full object-cover"
        aria-label="Luxury jewelry showcase video"
        poster="/hero_img.jpg" // Add fallback image
      >
        <source src={settings.videos[0]} type='video/mp4'/>
      </video>
    </div>
  )
}
