import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React from 'react'

export default function ErrorComponent({reset}) {
  const router=useRouter()
  const refreshFunction = (params) => {
    location.reload(true)
  }
  
  return (
    <div className='flex flex-col gap-3 min-w-[240px] w-fit px-5 h-fit items-center p-4 rounded-lg bg-gray-100 shadow-lg'>
      <Link href={`/`} className='flex items-center tracking-[4px] text-2xl capitalize px-5'>elephantisaland</Link>
      <span className='flex text-sm'>click below to reload the page</span>
      <button
        className='flex underline text-sm font-medium cursor-pointer px-5'
          onClick={
          // Attempt to recover by trying to re-render the segment
          () => reset()
          }
      >
          Try again
      </button>
      <button
        className='flex underline text-sm font-medium cursor-pointer px-5'
          onClick={
          // Attempt to recover by trying to re-render the segment
          () => refreshFunction()
          }
      >
          refresh page
      </button>
    </div>
  )
}
