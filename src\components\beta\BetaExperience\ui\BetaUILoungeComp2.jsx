import React from 'react'
import <PERSON>E<PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import BetaExperienceStairsPointComp from './BetaExperienceStairsPointComp'
import BetaExperienceInfoPointComp from './BetaExperienceInfoPointComp'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUILoungeComp2({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()

  const handleLounge = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[1].name})
  }

  const handleStairs = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[0].name})
  }
  const handleInfo = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
  }
  const handleVideo = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'video'})
  }
  return (
    <>
        {/* INFO BTN */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[5,12,-30]}
            occlude
        >
            <BetaExperienceInfoPointComp data={{height:settings.menuExp.readMore.height,width:settings.menuExp.readMore.width,image:settings.menuExp.readMore.btnIcons}} handleClick={handleInfo}/>
        </Html>
        {/* STAIRS */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-12,-2,-30]}
            occlude
        >
            <BetaExperienceStairsPointComp data={{height:settings.menuExp.upstairs.height,width:settings.menuExp.upstairs.width,image:settings.menuExp.upstairs.btnIcons}} handleClick={handleStairs}/>
        </Html>
        {/* TV*/}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[14,-8.5,-30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.infoVideo.height,width:settings.menuExp.infoVideo.width,image:settings.menuExp.infoVideo.btnIcons}} handleClick={handleVideo}/>
        </Html>
        {/* LOUNGE */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-20,-25,30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleLounge}/>
        </Html>
    </>
  )
}
