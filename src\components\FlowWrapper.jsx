'use client'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import React, { useEffect, useState } from 'react'
import { settings } from '@/libs/betasiteSettings'
import { checkThreeJSCompatibility } from '@/libs/threejsCompatibility'
import BetaVideoWrapper from './beta/BetaVideoWrapper'
import BetaExperience from './beta/BetaExperience/BetaExperience'

export default function FlowWrapper({data}) {
  const [webGlCompatibilty,setWebGlCompatibilty]=useState(true)
  const [checkingCompatibilty,setCheckingCompatibilty]=useState(false)
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  
  useEffect(()=>{
    setCheckingCompatibilty(true)
    setWebGlCompatibilty(checkThreeJSCompatibility())
    setCheckingCompatibilty(false)
  },[])

  // console.log('FlowWrapper:',!experienceState?.show360)
  // console.log('FlowWrapper:',!experienceState?.landinPageItems )
  return (
    <div className='flex w-full h-full'>
      {!experienceState?.show360 
        ? !experienceState?.landinPageItems ? 
            <BetaVideoWrapper data={data.videos}/> : null
        : <BetaExperience data={data._360s}/>}
    </div>
  )
}
