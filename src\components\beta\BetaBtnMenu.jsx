'use client'

import { ACTIONS_EXPERIENCE } from "@/libs/contextProviders/reducerBetaExperience"
import { useBetaContextExperience } from "@/libs/contextProviders/useBetaContextExperience"
import { useState } from "react"

export default function BetaBtnMenu() {
  // const {swap,setSwap,experienceState,experienceDispatch}=useExperienceContext()
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const [menuPop,setMenuPop]=useState(true)
  const [rotete45,setRotete45]=useState(false)
  
  const [rollOver,setRollOver]=useState(false)

  const handleClick = () => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_MENU})
    // experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO})
  }
  
  // console.log('BtnMenu:',menuPop,rollOver)
  return (
    <div 
      className='flex w-full h-full cursor-pointer items-center justify-center'
      onClick={handleClick}
    >
      <div className={`flex relative flex-col w-full h-full items-center justify-center`}>
        {!experienceState?.showMenu
          ? <div 
              onMouseEnter={()=>setRollOver(true)}
              onMouseLeave={()=>setRollOver(false)}
              className={`flex flex-col items-center gap-2 justify-center w-full h-full ${!experienceState?.showMenu ? 'gap-2' : 'gap-0'}`
              }
            >
              <div className={`w-8 duration-75 ease-linear bg-[#e7e0da] h-1`}/>
              <div className={`w-8 duration-75 ease-linear bg-[#e7e0da] h-1`}/>
              <div className={`w-8 duration-75 ease-linear bg-[#e7e0da] h-1`}/>
            </div>
          : <div className="flex items-center justify-center w-full h-full">
              <div className={`${!experienceState?.showMenu ? 'rotate-0' : 'rotate-45 '} bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
              <div className={`${!experienceState?.showMenu ? 'rotate-0' : '-rotate-45'} bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            </div>
        }
      </div>
    </div>
  )
}
