import DashboardWrapperSingle360 from "@/components/dashboard/DashboardWrapperSingle360";
import { settings } from "@/libs/siteSettings";

export default async function DashboardPage({params}) {
  const {id}=await params
  // console.log('Home:',)
  // const dataBd = await fetch(`${settings.url}/api/360s/${id}`)
  // const data = await dataBd.json()
  const data = settings._360sDb.find(({_id})=>_id==id)
  // const dataBdList = await fetch(`${settings.url}/api/360s`)
  // const dataList = await dataBdList.json()
  const dataList = settings._360sDb
  // console.log('DashboardPage:',data)
  return (
    <div className="flex w-full h-full bg-gray-100 rounded-md shadow-md overflow-hidden">
      <DashboardWrapperSingle360 data={data} dataList={dataList}/>
    </div>
  );
}
export const dynamic = 'force-dynamic'