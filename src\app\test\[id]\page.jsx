import TestWrapper from "@/components/testBuild/TestWrapper";
import { settings } from "@/libs/siteSettings";

export default async function TestSinglePage({params}) {
  const {id}=await params
  // const dataBd = await fetch(`${settings.url}/api/360s/${id}`)
  // const data = await dataBd.json()
    const data=settings?._360sDb?.find(({_id})=>_id==id)
    const dataList=settings?._360sDb
    // console.log('TestSinglePage:',data)
  return (
    <main className="flex relative w-full h-full overflow-hidden">
      <TestWrapper data={data} dataList={dataList}/>
    </main>
  );
}
export const dynamic = 'force-dynamic'