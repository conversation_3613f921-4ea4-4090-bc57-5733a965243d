export const INITIAL_SITE_STATE={
    showCart:false,
    showWishlist:false,
    landinPageItems:true,
    show360:false,
}

export const ACTIONS_SITE={
    TOGGLE_CART:'TOGGLE_CART',
    TOGGLE_WISHLIST:'TOGGLE_WISHLIST',
    TOGGLE_LANDING_PAGE:'TOGGLE_LANDING_PAGE',
    SHOW_360:'SHOW_360',
    ADD_TO_CART:'ADD_TO_CART'
}

export const reducerSite=(state,action)=>{
    switch (action.type) {
        case 'TOGGLE_CART':
            return{
                ...state,
                showCart:!state?.showCart,
                // showWishlist:false
            }     
        case 'TOGGLE_LANDING_PAGE':
            return{
                ...state,
                // showCart:false,
                landinPageItems:!state?.landinPageItems
            }
        case 'SHOW_360':
            return{
                ...state,
                // showCart:false,
                landinPageItems:false,
                show360:true
            }
        case 'TOGGLE_WISHLIST':
            return{
                ...state,
                // showCart:false,
                showWishlist:!state?.showWishlist
            }
        case 'ADD_TO_CART':
            return{
                ...state,
                // showCart:false,
            }
        default:
            state
            break;
    }
}