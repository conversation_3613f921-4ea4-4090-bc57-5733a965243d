import { Html } from '@react-three/drei'
import Image from 'next/image'
import React, { useState } from 'react'

export default function ExperiencePOIinfoVideoMarker({data,handleClick}) {
  const [swap,setSwap]=useState(true)
  console.log('ExperiencePOIinfoVideo:',data)
  return (
    <div 
      onMouseEnter={()=>setSwap(!swap)} 
      onMouseLeave={()=>setSwap(!swap)} 
      onClick={handleClick}
      className='btn flex relative items-center select-none justify-center cursor-pointer max-w-fit max-h-fit'
    >
      <div 
        // onClick={handleClick?.[index]} 
        className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
      >
        <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
      </div>
      <div
          // onClick={handleClick?.[index]} 
          className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
      >
        <Image className='flex-none' width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
      </div>
    </div>
  )
}