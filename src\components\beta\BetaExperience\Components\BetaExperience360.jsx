import React, { useEffect, useRef, useState } from 'react'
import BetaExperienceControls from './BetaExperienceControls'
import { BackSide, TextureLoader } from 'three'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { useLoader } from '@react-three/fiber'
import { degToRad } from 'three/src/math/MathUtils'
import BetaUIEntranceComp from '../ui/BetaUIEntranceComp'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'
import { settings } from '@/libs/betasiteSettings'
import BetaUILoungeComp from '../ui/BetaUILoungeComp'
import BetaUIBalcony from '../ui/BetaUIBalcony'
import BetaUITerrace from '../ui/BetaUITerrace'
import BetaUIEastView from '../ui/BetaUIEastView'
import BetaUINorthView from '../ui/BetaUINorthView'
import BetaUIWestView from '../ui/BetaUIWestView'
import BetaUISouthView from '../ui/BetaUISouthView'
import BetaUIOutdoors from '../ui/BetaUIOutdoors'
import BetaUIMasterBath from '../ui/BetaUIMasterBath'
import BetaUIMasterBed from '../ui/BetaUIMasterBed'
import BetaUIBedroom2 from '../ui/BetaUIBedroom2'
import BetaUIBedroom1 from '../ui/BetaUIBedroom1'
import BetaUIStairsComp from '../ui/BetaUIStairsComp'
import BetaUIPatioRight from '../ui/BetaUIPatioRight'
import BetaUIPatioleft from '../ui/BetaUIPatioleft'
import BetaUIDiningAComp from '../ui/BetaUIDiningAComp'
import BetaUIDiningComp from '../ui/BetaUIDiningComp'
import BetaUILoungeComp2 from '../ui/BetaUILoungeComp2'

function _360Comp({texture,textureArrayList}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const [values,setValues]=useState()
  const [showIcons,setShowIcons]=useState(false)
  const refObject=useRef()

  const onDragShowIcons = (params) => {
    setShowIcons(true)
  }
  
  const handleStepIn = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.SHOW_NAV})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[1].name})
  }
  
  useEffect(() => {
    setValues(...textureArrayList?.list?.filter(i=>i.name==experienceState?._360Name))
  }, [experienceState?._360Name])
  
  // console.log('BetaExperience360 _360Comp:',experienceState?._360Name)
  return(
    <>
      <mesh
        ref={refObject}
        name='360Sphere'
        onPointerDown={onDragShowIcons}
        scale={[1,1,-1]}
        // rotation={[0,degToRad(290),0]}
        rotation={[0,degToRad(values?.rotY),0]}
      >
        <meshBasicMaterial
          map={texture}
          side={BackSide}
        />
        <sphereGeometry
          args={[128,128,128]}
        />
      </mesh>
      {experienceState?._360Name=='entrance' && <BetaUIEntranceComp showIcons={showIcons} handleStepIn={handleStepIn}/>}
      {experienceState?._360Name=='lounge' && !experienceState?.showMenu  && <BetaUILoungeComp showIcons={showIcons}/>}
      {experienceState?._360Name=='lou' && !experienceState?.showMenu  && <BetaUILoungeComp2 showIcons={showIcons}/>}
      {experienceState?._360Name=='dining' && !experienceState?.showMenu && <BetaUIDiningComp showIcons={showIcons}/>}
      {experienceState?._360Name=='din' && !experienceState?.showMenu && <BetaUIDiningAComp showIcons={showIcons}/>}
      {experienceState?._360Name=='patio left' && !experienceState?.showMenu && <BetaUIPatioleft showIcons={showIcons}/>}
      {experienceState?._360Name=='patio right' && !experienceState?.showMenu && <BetaUIPatioRight showIcons={showIcons}/>}
      {experienceState?._360Name=='first floor' && !experienceState?.showMenu && <BetaUIStairsComp showIcons={showIcons}/>}
      {experienceState?._360Name=='bedroom 1' && !experienceState?.showMenu && <BetaUIBedroom1 showIcons={showIcons}/>}
      {experienceState?._360Name=='bedroom 2' && !experienceState?.showMenu && <BetaUIBedroom2 showIcons={showIcons}/>}
      {experienceState?._360Name=='master bedroom' && !experienceState?.showMenu && <BetaUIMasterBed showIcons={showIcons}/>}
      {experienceState?._360Name=='masterbath' && !experienceState?.showMenu && <BetaUIMasterBath showIcons={showIcons}/>}
      {experienceState?._360Name=='the outdoors' && !experienceState?.showMenu && <BetaUIOutdoors showIcons={showIcons}/>}
      {experienceState?._360Name=='south view' && !experienceState?.showMenu && <BetaUISouthView showIcons={showIcons}/>}
      {experienceState?._360Name=='west view' && !experienceState?.showMenu && <BetaUIWestView showIcons={showIcons}/>}
      {experienceState?._360Name=='north view' && !experienceState?.showMenu && <BetaUINorthView showIcons={showIcons}/>}
      {experienceState?._360Name=='east view' && !experienceState?.showMenu && <BetaUIEastView showIcons={showIcons}/>}
      {experienceState?._360Name=='balcony' && !experienceState?.showMenu && <BetaUIBalcony showIcons={showIcons}/>}
      {experienceState?._360Name=='terrace' && !experienceState?.showMenu && <BetaUITerrace showIcons={showIcons}/>}
    </>
  )
}

function LoadTexuturesComp({textureArrayList,textureArray}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const [texture,setTexture]=useState()
  let textureNameArray=[]
  let texturePathArray=[]

  textureArrayList?.list?.length>0 && textureArrayList?.list?.map(i=>{
    textureArray.push(i.name)
    textureNameArray.push(i.name)
    texturePathArray.push(i.url)
  })
  
  textureArray=useLoader(TextureLoader,texturePathArray)

  textureArray?.map((i,index)=>i.name=textureNameArray[index])

  const filterTexture = () => {
    // console.log('filterTexture fn',textureArray)
    textureArray?.length>0 && setTexture(...textureArray?.filter(i=>i.name==experienceState._360Name))
  }
  
  useEffect(() => {
    // console.log('name filter')
    textureArray?.length>0 && filterTexture()
  }, [textureArray,experienceState?._360Name])

  // console.log('LoadTexuturesComp:',textureArrayList)
  return(
    <>
      {texture && <_360Comp texture={texture} textureArrayList={textureArrayList}/>}
    </>
  )
}

export default function BetaExperience360({data}) {
  // console.log('Experience360',data)
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const [cameraPosition,setCameraPosition]=useState('0,0,1')
  const [textureArrayList,setTextureArrayList]=useState([])
  const [toggleRefresh,setToggleRefresh]=useState(false)
  const [rotY,setrotY]=useState(90)
  let textureArray=[]

  const fiilterLocationTextures = () => {
    // console.log(experienceState?._360Location)
    return data?.filter(i=>i.name==experienceState?._360Location)
  }

  // LOCATION LIST 
  useEffect(()=>{
    setTextureArrayList(...fiilterLocationTextures())
  },[experienceState?._360Location])

  // LOCATION LIST 
  useEffect(()=>{
    setToggleRefresh(!toggleRefresh)
    setCameraPosition(toggleRefresh ? '0,0,.99' : '0,0,1')
  },[experienceState._360Name])

  // console.log(
  //   'Experience360',
  //   textureArrayList,
  // )

  return (
    <>
      <BetaExperienceControls cameraPosition={cameraPosition} rotateSpeed={-.5}/>
      <LoadTexuturesComp data={data} textureArrayList={textureArrayList} textureArray={textureArray}/>
    </>
  )
}
