import { Html } from '@react-three/drei'
import React, { useEffect, useMemo, useRef } from 'react'
import { BackSide } from 'three'
import { degToRad } from 'three/src/math/MathUtils'
import TextureLoaderSpiner from './TextureLoaderSpiner'
import { useDashboardContextComponent } from '@/libs/contextProviders/useDashboardContextComponent'
import { useControls } from 'leva'

export default function Test360({texture}) {
    const {
        dataArrayList,setDataArrayList,
        textureId,setTextureId,
        textureArray,setTextureArray,
        textureObject,setTextureObject,
        dataObject,setDataObject,
        texturesLoaded,setTexturesLoaded,
    }=useDashboardContextComponent()

    const refTextureObject=useRef()

    const options = useMemo(() => {
          return {
            y: { value: dataObject?._360Rotation!=0 ? dataObject?._360Rotation : 0, min: -0, max: 360, step: 0.001 },
          }
        }, [])
        
        const rotation = useControls('360 rotate', options)

    useEffect(() => {
        setDataObject({...dataObject,_360Rotation:rotation.y})
    }, [rotation?.y])

    // console.log('Test360 dataObject:',refTextureObject.current)

    return (
        <mesh
            ref={refTextureObject}
            position={[0, 0, 0]}
            rotation={[0, degToRad(dataObject?._360Rotation!==0 || dataObject?._360Rotation===undefined ? dataObject?._360Rotation : rotation.y), 0]}
            scale={[1, 1, -1]}
        >
        <sphereGeometry
            args={[128, 128, 128]}
        />
        <meshBasicMaterial
            map={texture}
            side={BackSide}
        />
        </mesh>
    )
}
