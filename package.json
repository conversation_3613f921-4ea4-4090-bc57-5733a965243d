{"name": "elepantislandsbeta", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p3002", "build": "next build", "start": "next start -p3002", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@types/three": "^0.175.0", "firebase": "^11.6.0", "leva": "^0.10.0", "mongoose": "^8.13.2", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "three": "^0.175.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4"}}