
function isWebGLAvailable() {
    try {
        const canvas = document.createElement('canvas');
        return !!window.WebGLRenderingContext && (
            canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
        );
    } catch (e) {
        return false;
    }
}

function checkThreeJSCompatibility() {
    // console.log(isWebGLAvailable)
    if (isWebGLAvailable()) {
        // console.log('Three.js is compatible with this browser.');
        return true
    } else {
        // console.log('Three.js is not compatible with this browser.');
        return false
    }
}

// Export the function for use in other modules
export { checkThreeJSCompatibility };
