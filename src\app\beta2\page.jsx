import LandinPageImage from "@/components/LandinPageImage";
import { settings } from "@/libs/siteSettings";
import Image from "next/image";

export default async function BetaHome() {
  // console.log('Home:',)
  let data=null
  // const dataBd = await fetch(`${settings.url}/api/360s`)
  // data = await dataBd.json()
  data=settings._360sDb
  console.log('Home:',data)
  return (
    <div className="flex w-full items-center justify-center h-full overflow-hidden">
      <div className="flex w-full h-full overflow-scroll">
        {data?.map((i,index)=>  
          <div key={index} className="flex absolute w-full h-full overflow-hidden">
            <div className="flex relative w-full h-full">
              <Image  alt="" priority src={i?.url} className="object-contain" width={3040} height={1520}/>
            </div>
          </div>
        )}
      </div>
      <main className="flex z-10 absolute top-0 left-0 w-full h-dvh overflow-hidden">
        <LandinPageImage data={data}/>
      </main>
    </div>
  );
}
export const dynamic = 'force-dynamic'