import DashboardAddButton from '@/components/dashboard/DashboardAddButton'
import DashboardContextProvider from '@/libs/contextProviders/useContextDashboard'
import React from 'react'

export default function layout({children}) {
  return (
    <DashboardContextProvider>
      <div className='flex flex-col h-full w-full bg-gray-50 p-5 px-10 mb-5'>
        <div className='flex relative w-full h-20 items-center justify-between'>
          <span className='flex capitalize font-bold text-xl'>dashboard</span>
          <DashboardAddButton/>
        </div>
        {children}
      </div>
    </DashboardContextProvider>
  )
}
