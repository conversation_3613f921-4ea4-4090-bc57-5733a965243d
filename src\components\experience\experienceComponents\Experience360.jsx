import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import React, { useEffect, useRef } from 'react'
import { BackSide } from 'three'
import { degToRad } from 'three/src/math/MathUtils'

export default function Experience360({texture,setShowLoader}) {
    const {
        experienceStateList,setExperienceStateList,
        dataObject
    }=useExperienceContext()

    const refObject=useRef()
    const refmaterial=useRef()

    // useEffect(() => {
    //     console.log('Experience360 loaded:',refmaterial.current?.map?.source?.data)
    //     return () => {
    //     console.log('Experience360 unloaded:',)
    //     }
    // }, [refmaterial])

    // console.log('Experience360:',refmaterial.current)
  return (
    <>
      <mesh
        ref={refObject}
        name='360Sphere'
        scale={[1,1,-1]}
        rotation={[0,degToRad(dataObject?._360Rotation==0 ? 0 : dataObject?._360Rotation),0]}
      >
        <sphereGeometry
            args={[128,128,128]}
        />
        <meshBasicMaterial
          ref={refmaterial}
          map={texture} 
          side={BackSide}
        />
      </mesh>
    </>
  )
}
