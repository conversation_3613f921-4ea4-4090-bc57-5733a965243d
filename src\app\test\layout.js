import DashboardAddButton from "@/components/dashboard/DashboardAddButton";
import DashboardContextProvider from "@/libs/contextProviders/useDashboardContextComponent";

export default function layout({children}) {
  return (
    <DashboardContextProvider>
      <div className="flex flex-col w-full h-full bg-gray-50 overflow-hidden mb-10 px-10">
        <div className="flex flex-1/12 capitalize font-bold w-full min-h-10 items-center justify-between">
          dashboard
          <DashboardAddButton/>
        </div>
        <div className="flex flex-11/12 mb-10 w-full min-h-10 items-center">
            {children}
        </div>
      </div>
    </DashboardContextProvider>
  )
}
