import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { OrbitControls } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import React, { useEffect, useRef } from 'react'
import { degToRad } from 'three/src/math/MathUtils'

export default function BetaExperienceControls({cameraPosition,rotateSpeed,data}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const controlsRef=useRef(null)
  const {camera}=useThree()

  const centerCameraControlsView = (position) => {
    camera.position.set(...position?.split(',').map(i=>parseFloat(i)))
    if(controlsRef.current){
      controlsRef.current.target.set(0,0,0)
      controlsRef.current.update()
    }
  }
  
  useEffect(()=>{
    centerCameraControlsView(cameraPosition)
  },[cameraPosition])

  console.log('ExperienceControls:',camera)
  return (
    <>
      <OrbitControls
        ref={controlsRef}
        enablePan={false}
        maxPolarAngle={degToRad(120)}
        minPolarAngle={degToRad(60)}
        maxDistance={0.5}
        minDistance={0}
        enableDamping={true}
        rotateSpeed={rotateSpeed}
      />
    </>
  )
}
