import Image from 'next/image';
import React, { useEffect, useState } from 'react';

export default function ExperiencePOIpositionMarker({data,handleClick}) {
  const [swap,setSwap]=useState(true)
  const [show,setShow]=useState(false)

  useEffect(() => {
    setTimeout(() => {
      setShow(true)
    }, 3000);
  }, [])

  // console.log('ExperiencePOIpositionMarker:',data)
  return (
    <>
    {show 
        &&<div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            onClick={handleClick}
            className='btn flex relative items-center select-none justify-center cursor-pointer image max-w-[60px] min-w-[60px] min-h-[60px] max-h-[60px]'
          >
            <div 
              // onClick={handleClick?.[index]} 
              className={`image ${swap ? 'hidden' : 'flex'} top-0 left-0 w-full h-full image`}
            >
              <Image 
                fill
                alt='button images for landpage options' 
                src={data?.btnIcons?.ov}
              />
            </div>
            <div
                // onClick={handleClick?.[index]} 
                className={`image ${swap ? 'flex' : 'hidden'} top-0 left-0 w-full h-full image`}
            >
              <Image 
                className='flex-none' 
                fill
                alt='button images for landpage options' 
                src={data?.btnIcons?.off}
              />
            </div>
          </div>
        }
    </>
  )
}