'use client'

import { ACTIONS_EXPERIENCE } from "@/libs/contextProviders/reducerBetaExperience"
import { useBetaContextExperience } from "@/libs/contextProviders/useBetaContextExperience"
import Image from "next/image"
import { useState } from "react"

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <Image width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <Image width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function BetaBookNowBtn() {
    const [swap,setSwap]=useState(true)
    const {experienceState,experienceDispatch}=useBetaContextExperience()

    const handleBookNow = (params) => {
    //   console.log('BookNowBtn handleBookNow fn')
        experienceDispatch({type:ACTIONS_EXPERIENCE.MENU_TYPE,payload:'booknow'})
    }
    
  return (
    <div onClick={handleBookNow} className='flex w-full h-full justify-end items-center'>
        <BtnLandingpageComponent data={{width:104,height:46,btnIcons:{off:'/book_btn_off.png',ov:'/book_btn_ov.png'}}}/>
    </div>
  )
}
