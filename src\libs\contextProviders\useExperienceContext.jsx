'use client'
import { createContext, useContext, useReducer, useState } from "react"
import { INITIAL_EXPERIENCE_STATE, reducerExperience } from "./reducerExperience"

const ExperienceContext=createContext()

export default function ExperienceContextProvider({children}) {
    const [experienceState,experienceDispatch]=useReducer(reducerExperience,INITIAL_EXPERIENCE_STATE)
    const [experienceStateList,setExperienceStateList]=useState([])
    const [webGlCompatibilty,setWebGlCompatibilty]=useState(true)
    const [dataObject,setDataObject]=useState({_360Rotation:0,cameraPosition:0})
    const [dataUpdate,setDataUpdate]=useState()
    return (
        <ExperienceContext.Provider
            value={{
                experienceStateList,setExperienceStateList,
                dataUpdate,setDataUpdate,
                dataObject,setDataObject,
                experienceState,experienceDispatch,
                webGlCompatibilty,setWebGlCompatibilty
            }}
        >
            {children}
        </ExperienceContext.Provider>
    )
}

export const useExperienceContext=()=>{
    const context=useContext(ExperienceContext)
    if(!context){
        throw new Error('useExperienceContext must be used within an ExperienceProvider')
    }
    return context
}