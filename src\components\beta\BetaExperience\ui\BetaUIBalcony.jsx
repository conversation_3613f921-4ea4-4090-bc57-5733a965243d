import React from 'react'
import { Html } from '@react-three/drei'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'

export default function BetaUIBalcony  ({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
   const habdleOutdoor_008 = () => {
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
      experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[4].name})
    }
  return (
    <>
      {/* LEFT LAMP */}
      <Html
        className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
        position={[35,-7,-60]}
        occlude
      >
        <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={habdleOutdoor_008}/>
      </Html>
    </>
  )
}
