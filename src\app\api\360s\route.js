import { connectToElephantislandDB } from "@/libs/mongoDb/connectToElephantislandDB";
import { _360Settings } from "@/libs/mongoDb/models/_360Settings";
import { NextResponse } from "next/server";

export async function GET(req,{searchParams}) {
    const filters=await req.nextUrl.searchParams
    const collections=filters.get("collections")
    console.log('360s route:',collections)
    connectToElephantislandDB()
    try {
        const _360=await _360Settings.find({}).sort({name:1})
        // const _360=await _360Settings.find({})
        return NextResponse.json(_360,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get 360s',{status:500})
    }
}

export async function POST(req) {
    const body=await req.json()
    console.log('360 api route',body)
    connectToElephantislandDB()
    try {
        const found360=await _360Settings.findOne({name:body?.name})
        if(found360) return NextResponse.json('360 already exists',{status:501})
        const new360=await _360Settings(body)
        new360.save()
        console.log(new360)
        return NextResponse.json(new360,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get 360s',{status:500})
    }
}

export async function DELETE(req) {
    const body=await req.json()
    console.log('site api route',body)
    connectToElephantislandDB()
    try {
        await _360Settings.deleteMany({})
        return NextResponse.json('deleted all the 360 entries',{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to delete 360s',{status:500})
    }
}