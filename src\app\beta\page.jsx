import FlowWrapper from "@/components/FlowWrapper";
import BetaLandinPageImage from "@/components/beta/BetaLandinPageImage";
import { settings } from "@/libs/betasiteSettings";
import BetaMenuPopup from "@/components/beta/BetaMenuPopup";
import BetaBooknowMenuPopup from "@/components/beta/BetaBooknowMenuPopup";
export default async function BetaPage() {
  // const dataBd = await fetch(`${settings.url}/api/360`)
  // const data = await dataBd.json()
    const data=settings
    // console.log('Home:',data)
  return (
    <main className="flex relative w-full h-full overflow-hidden">
      <BetaLandinPageImage/>
      <FlowWrapper data={{videos:settings?.videos,_360s:data?._360s}}/>
      <BetaMenuPopup/>
      <BetaBooknowMenuPopup/>
    </main>
  );
}
export const dynamic = 'force-dynamic'