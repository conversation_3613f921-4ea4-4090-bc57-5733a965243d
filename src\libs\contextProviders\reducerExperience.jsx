export const INITIAL_EXPERIENCE_STATE={
    _360Name:'entrance',
    _360Location:'entrance',
    _360Object:{},
    showMenu:false,
    showNavbar:false,
    gallertShow:false,
    showExperience:true,
    videoInfo:'',
    infoText:'',
}

export const ACTIONS_EXPERIENCE={
    _360_NAME:'360_NAME', 
    _360_LOCATION:'_360_LOCATION', 
    _360_OJECT:'360_OJECT', 
    TOGGLE_MENU:'TOGGLE_MENU', 
    SHOW_NABAR:'SHOW_NABAR', 
    POPUP_VIDEO:'POPUP_VIDEO', 
    POPUP_GALLERY:'POPUP_GALLERY', 
    POPUP_INFO:'POPUP_INFO', 
    HIDE_EXPERIENCE:'HIDE_EXPERIENCE', 
    RESET:'RESET', 
}

export const reducerExperience=(state,action)=>{
    switch (action.type) {
        case 'POPUP_VIDEO':
            return{
                ...state,
                videoInfo:action,payload,
            }
        case 'POPUP_GALLERY':
            return{
                ...state,
                showNavbar:true,
            }
        case 'POPUP_INFO':
            return{
                ...state,
                infoText:action,payload,
            }
        case 'SHOW_NABAR':
            return{
                ...state,
                showNavbar:true,
            }
        case 'TOGGLE_MENU':
            return{
                ...state,
                showMenu:!state.showMenu,
            }
        case 'TOGGLE_MENU':
            return{
                ...state,
                showMenu:!state.showMenu,
            }
        case '_360_LOCATION':
            return{
                ...state,
                _360Location:action.payload,
            }
        case '360_NAME':
            return{
                ...state,
                _360Name:action.payload,
            }
        case '360_OJECT':
            return{
                ...state,
                _360Object:action.payload,
            }
        case 'HIDE_EXPERIENCE':
            return{
                ...state,
                showExperience:false,
            }
        case 'RESET':
            return{
                ...state,
                _360Name:'entrance',
                _360Location:'entrance',
                showMenu:false,
                showNavbar:false,
                // showCart:false,
            }
        default:
                state
            break;
    }
}