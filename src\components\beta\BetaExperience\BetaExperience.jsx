'use client'
import { useEffect, useRef, useState } from 'react'
import { checkThreeJSCompatibility } from '@/libs/threejsCompatibility'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import BetaNavbarComponent from '../BetaNavbarComponent'
import BetaExperienceWorld from './BetaExperienceWorld'

export default function BetaExperience({data}) {
  const [webGlCompatibilty,setWebGlCompatibilty]=useState(true)
  const [checkingCompatibilty,setCheckingCompatibilty]=useState(false)
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const divRef=useRef()

  useEffect(()=>{
    setCheckingCompatibilty(true)
    setWebGlCompatibilty(checkThreeJSCompatibility())
    setCheckingCompatibilty(false)
  },[])

  // console.log('Experience:',experienceState?._360Name!=='entrence')
  return (
    <>
      {webGlCompatibilty 
        ? <div className={`${experienceState?.show360 ? 'flex' : 'hidden'} w-full h-full`}> 
            {/* <Experience360Ui data={data}/> */}
            {experienceState?.showNav && <BetaNavbarComponent/>}
            {/* <NavbarComponent/> */}
            <BetaExperienceWorld data={data}/>
          </div>
        : <span className='flex w-full h-full items-center justify-center mt-20 text-gray-500 font-semibold text-sm italic'>
            This broweser is not compatible with the 3D experience, please try using a different browser
          </span>
      }
    </>
  )
}
