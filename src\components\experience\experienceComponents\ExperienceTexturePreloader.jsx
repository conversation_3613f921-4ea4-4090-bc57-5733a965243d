'use client'
import { Canvas, useLoader } from '@react-three/fiber'
import React from 'react'
import { TextureLoader } from 'three'

function TexturePreload({data}) {
    // const {
    //     experienceStateList,setExperienceStateList,
    //     dataObject,setDataObject,
    //     dataUpdate,setDataUpdate,
    //   }=useDashboardContext()
      
      // console.log('ExperienceTextureLoader:',[dataObject])
      
      let textureArrayList=[]
      let texturePathList=[]
      let textureNameList=[]
  
      data?.map(i=>{
        textureArrayList.push(i?.name)
        texturePathList.push(i?.url)
        textureNameList.push(i?.name)
      })
  
      // dataUpdate?.map(i=>{
      //   textureArrayList.push(i?.name)
      //   texturePathList.push(i?.url)
      //   textureNameList.push(i?.name)
      // })
  
      textureArrayList=useLoader(TextureLoader,texturePathList)
  
      textureArrayList.map((i,index)=>i.name=textureNameList[index])
  
      console.log('ExperienceTextureLoader:',textureArrayList[0])
      console.log('ExperienceTextureLoader:',dataArray)
    return(
        <></>
    )
}

export default function ExperienceTexturePreloader({data}) {
    
  return (
    <Canvas>
      <TexturePreload data={data}/>
    </Canvas>
  )
}
