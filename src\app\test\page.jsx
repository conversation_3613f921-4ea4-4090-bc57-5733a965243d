import TestWrapper from '@/components/testBuild/TestWrapper'
import { settings } from '@/libs/siteSettings'
import React from 'react'

export default async function PageTest() {
  // const dataBd = await fetch(`${settings.url}/api/360s`)
  // const data = await dataBd.json()
  const data=settings._360sDb
  // console.log('PageTest:',data)
  return (
    <div className='flex text-white items-center justify-center h-full w-full'>
      <TestWrapper data={data}/>
    </div>
  )
}
export const dynamic = 'force-dynamic'