import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { BsTrash } from 'react-icons/bs'
import TestInputComponent from './TestInputComponent'
import { usePathname, useRouter } from 'next/navigation'
import TestInput360SettingsComponent from './TestInput360SettingsComponent'
import { useDashboardContextComponent } from '@/libs/contextProviders/useDashboardContextComponent'
import TextureLoaderSpiner from '../TextureLoaderSpiner'

export default function TestInputWrapper({children}) {
    const {
        setTextureId,textureId,
        textureObject,
        dataObject,setDataObject,
        texturesLoaded,setTexturesLoaded,
        textureArray,setTextureArray,
        dataArrayList,setDataArrayList,
        refresh,setRefresh,
        tag,setTag,
        setSearchInput,searchInput,
    }=useDashboardContextComponent()

    const pathname=usePathname()
    const router=useRouter()
    const [onImageLoaded,setOnImageLoaded]=useState(false)
    

    const handleDelete = (id) => {
        // console.log(id)
        setDataArrayList(dataArrayList?.filter(i=>i?.name!==id))
    }

    const handleDoublClick = (id) => {
        // console.log(id)
        router.push(`/test/${dataObject?._id}`)
    }

    useEffect(() => {
        textureArray?.map(i=>{
            // console.log(i?.name)
            i?.name?.length>0 && setRefresh(!refresh)
        })
    }, [textureArray?.length>0])

    useEffect(() => {
        setDataObject(dataArrayList?.find(({name})=>name==textureId))
    }, [textureId])
    
    // console.log('TestInputWrapper:',pathname?.split('/').length-1<2)
  return (
    <div className='flex w-full h-full shadow rounded-lg bg-gray-300 overflow-hidden'>
        <div className='flex relative w-full h-full bg-gray-400 overflow-hidden'>

            {/* texture list */}
           {pathname?.split('/').length-1<2 && <div className='flex z-10 flex-col relative top-0 left-0 w-1/6 h-full items-center text-xs font-bold bg-gray-300 text-white p-2 overflow-y-auto gap-2'>
                {textureArray?.filter(i=>i?.name?.toLowerCase()?.includes(searchInput?.toLowerCase()))?.map((i,index)=>
                    <div onDoubleClick={e=>handleDoublClick(i?._id)} onClick={e=>setTextureId(i?.name)} key={index} className={`flex select-none cursor-pointer w-full min-h-14 max-h-16 items-center justify-between ${i?.name && 'bg-black/75'} hover:bg-gray-900/35 bg-gray-900/50 ease-linear gap-2 duration-200 rounded-md capitalize overflow-hidden p-2`}>
                        {i?.name?.length>0 ? <span className='text-wrap text-xs'>{i?.name?.replace('_',' ')}</span> : <TextureLoaderSpiner/>}

                        {/* delete button for the list */}
                        <div onClick={e=>handleDelete(i?.name)} className='flex text-xs items-center justify-center h-full cursor-pointer border-2 border-white rounded-md px-1 w-fit'>
                            <BsTrash className='text-lg'/>
                        </div>
                        {/* {console.log(i?.name)} */}
                    </div>
                )}
            </div>}

            {/* 360 texture viewer */}
            <div className={`flex relative ${pathname?.split('/').length-1<2 ? 'w-5/6' : 'w-full'} h-full`}>
                {<div className='flex flex-col gap-2 text-xs absolute top-2 left-2 w-fit h-fit z-10 px-4 font-bold text-white p-2 rounded-md'>
                    {pathname?.split('/').length-1<2 && <span className='text-xs bg-black/75 rounded-md p-3'>{tag}</span>}
                    {textureObject?.name?.length>0 && <div className='flex relative capitalize flex-col gap-1 w-fit max-h-fit z-10 px-4 text-xs font-bold text-white p-3 rounded-md bg-black/75'>
                        <span className='text-lg'>{textureObject?.name?.replace('_',' ')}</span>
                        <span className='text-xs'>cam Position: {dataObject?.cameraPosition?.length>0 ? dataObject?.cameraPosition : 0}</span>
                        <span className='text-xs'>360 Y Rotation: {dataObject?._360Rotation?.length>0 ? dataObject?._360Rotation : 0}</span>
                    </div>}
                </div>}
                <div className='flex capitalize absolute top-2 right-2 w-fit h-10 z-10 px-4 items-center text-xs font-bold text-white rounded-md'>
                    <button onClick={e=>setRefresh(!refresh)} className='flex w-fit px-4 h-full capitalize hover:bg-gray-700 bg-gray-900 duration-200 ease-linear rounded-md text-white cursor-pointer items-center justify-center'>reset view</button>
                </div>
                {children}
            </div>
        </div>

        {/* input section */}
        <div className='flex w-2/6 h-full p-2 gap-2 flex-col overflow-y-auto'>
            {pathname?.split('/').length-1>1 ? <TestInput360SettingsComponent/> : <TestInputComponent/>}
        </div>
    </div>
  )
}
