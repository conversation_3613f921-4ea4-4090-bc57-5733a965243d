import ExperienceWorld from "@/components/experience/ExperienceWorld";
import { settings } from "@/libs/siteSettings";
import Image from "next/image";

export default async function BetaHome() {
  // console.log('Home:',)
  let data=null
  // const dataBd = await fetch(`${settings.url}/api/360s`)
  // data = await dataBd.json()
  data=settings._360sDb
  // console.log('Home:',data)
  return (
    <div className="flex h-full items-center justify-center w-full">
      <div className="flex fixed z-10 bottom-0 left-0 w-fit h-fit">
        <div className="flex relative w-fit h-fit">
          {data?.map((i,index)=>
            <div key={index} className="flex absolute w-fit h-fit">
              <div className="flex relative min-w-[3050px] max-h-fit max-w-fit min-h-[1520px]">
                <Image alt="" priority src={i?.url} className="image360 object-contain" fill/>
              </div>
            </div>
          )}
        </div>
      </div>
      <main className="flex relative w-full h-full overflow-hidden">
        <ExperienceWorld data={data}/>
      </main>
    </div>
  );
}
export const dynamic = 'force-dynamic'