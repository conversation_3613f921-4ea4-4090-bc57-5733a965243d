import { OrbitControls } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import { useControls } from 'leva'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { degToRad } from 'three/src/math/MathUtils'

export default function DashboardExperienceControls({}) {    
    const refControls=useRef()
    const {camera}=useThree()

    const options = useMemo(() => {
      return {
        y: { value: dataObject?.cameraPosition!=0 ? dataObject?.cameraPosition : 0, min: -1, max: 1, step: 0.001 },
      }
    }, [])
    
    const target = useControls('Camera Look Up/Down', options)
    
    const centerCameraControlsView = () => {
      camera.position.set(0,0,1)
      if(refControls.current){
        refControls.current.target.set(0,0,0)
        refControls.current.update()
      }
    }
     
    // UPDATE DATAOBJECT ENTRIES
    useEffect(()=>{
      dataObject?.cameraPosition==0 && setDataObject({...dataObject,cameraPosition:target.y})
    },[target.y])
     
    // RESET THE CAMERA TO LOOK IN PARTICULAR DIRECTION
    useEffect(()=>{
      centerCameraControlsView()
    },[dashboardStateEdit?.restViewToggle])
      
    // console.log('DashboardExperienceControls controls:',dataObject?.cameraPosition!=0 ? dataObject?.cameraPosition : 0)
  return (
    <OrbitControls
      ref={refControls}
      enablePan={false}
      maxPolarAngle={degToRad(120)}
      minPolarAngle={degToRad(60)}
      maxDistance={0.5}
      minDistance={0}
      enableDamping={true}
      target={[0,target.y, -1]}
      rotateSpeed={-.5}
    />
  )
}
