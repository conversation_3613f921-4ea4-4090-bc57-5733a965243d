import React from 'react'
import <PERSON>E<PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUIEastView  ({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
   const habdleOutdoor_008 = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
      }
  return (
    <>
      {/* LEFT LAMP */}
      <Html
        className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
        position={[-9,-14,-60]}
        occlude
      >
        <BetaExperienceSnapPointComp data={{height:settings.menuExp.readMore.height,width:settings.menuExp.readMore.width,image:settings.menuExp.readMore.btnIcons}} handleClick={habdleOutdoor_008}/>
      </Html>
    </>
  )
}