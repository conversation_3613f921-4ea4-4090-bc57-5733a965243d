'use client'

import { Canvas } from "@react-three/fiber"
import { Suspense, useEffect, useState } from "react"
import DashboardExperienceTextureLoader from "./dashboardExperience/DashboardExperienceTextureLoader"
import DashboardExperienceControls from "./dashboardExperience/DashboardExperienceControls"
import { useDashboardContext } from "@/libs/contextProviders/useContextDashboard"
import { Leva } from "leva"
import { Html } from "@react-three/drei"
import { usePathname } from "next/navigation"


const Spinner = ({ size = 'h-6 w-6', color = 'text-gray-500', borderWidth = 'border-2', className = '' }) => {
  return (
    <div className="flex items-center justify-center w-full h-full">
      <div className={`animate-spin rounded-full ${borderWidth} border-t-transparent ${color} ${size} ${className}`}></div>
    </div>
  );
};

export default function DashboardExperienceWorld() {
    const {
      experienceState,experienceDispatch,
      dataUpdate,setDataUpdate,
      dataObject,setDataObject,refresh360
    }=useDashboardContext()

    const [scrollIndex,setScrollIndex]=useState(0)
    const [showLoader,setShowLoader]=useState(false)
    const pathname=usePathname()

    useEffect(() => {
      // console.log('DashboardExperienceWorld:', 'refresh')
    }, [refresh360])
    
    // console.log('DashboardExperienceWorld:',dataObject)
  return (
    <div className="experienceWorld flex relative w-full h-full items-center justify-center">
      <Leva
        collapsed // default = false, when true the GUI is collpased
      />
      <Canvas>
        <Suspense fallback={<Html center><Spinner/></Html>}>
          {dataObject?.url?.length>0 ? <DashboardExperienceTextureLoader/> : <Html center><span className="text-sm text-nowrap text-gray-500">Select a 360</span></Html>}
          <DashboardExperienceControls/>
        </Suspense>
      </Canvas>
    </div>
  )
}
