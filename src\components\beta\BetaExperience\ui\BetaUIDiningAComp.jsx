import React from 'react'
import BetaE<PERSON>rance360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import BetaExperienceInfoPointComp from './BetaExperienceInfoPointComp'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUIDiningAComp({handleClick}) {
    const {experienceState,experienceDispatch}=useBetaContextExperience()

    const handleDining = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[2].name})
    }

    const handleInfoBtnDining = () => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
    }
  return (
    <>
         {/* INFO BTN */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[6,-11,-30]}
            occlude
            >
            <BetaExperienceInfoPointComp data={{height:settings.menuExp.readMore.height,width:settings.menuExp.readMore.width,image:settings.menuExp.readMore.btnIcons}} handleClick={handleInfoBtnDining}/>
        </Html>
        {/* DINING2 */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[1,-4,-30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleDining}/>
        </Html>
    </>
  )
}
