import BetaLoadingComponent from '@/components/beta/BetaLoadingComponent'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { Html, useProgress } from '@react-three/drei'
import React, { useEffect } from 'react'

export default function BetaExperienceLoader() {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
    const {progress}=useProgress()
    useEffect(()=>{progress===100 && experienceDispatch({type:ACTIONS_EXPERIENCE.TEXTURES_LOADED})},[progress])
    // console.log(progress)
  return (
    <Html className='text-gray-500 text-xl' center={true}>
      <BetaLoadingComponent/>
    </Html>
  )
}
