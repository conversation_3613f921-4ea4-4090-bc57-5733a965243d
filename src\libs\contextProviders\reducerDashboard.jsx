export const INITIAL_DASHBOARD_STATE={
    _360Index:0,
    locationName:0,
    _360Name:'',
    _360Id:0,
    infoID:'',
    POIType:'',
    showPOI:false,
    landingPagePOI:false,
    _360POI:false,
    infoPOI:false,
    levelPOI:false,
    controlsRest:false,
    restViewToggle:false,
}

export const ACTIONS_DASHBOARD={
    _360_INDEX:'_360_INDEX',
    LOCATION_360:'LOCATION_360',
    _360_NAME:'_360_NAME',
    _360_URL:'_360_URL',
    _360_ID:'_360_ID',
    ID_NAME:'ID_NAME',
    POI_TYPE:'POI_TYPE',
    SHOW_POI:'SHOW_POI',
    LANDING_POI:'LANDING_POI',
    _360_POI:'360_POI',
    INFO_POI:'INFO_POI',
    LEVEL_POI:'LEVEL_POI',
    RESET_STATE:'RESET_STATE',
    RESET_VIEW:'RESET_VIEW',
}

export const reducerDashboard=(state,action)=>{
    switch (action.type) {
        case '_360_INDEX':
            return{
                ...state,
                _360Index:state.payload,
                // showWishlist:false
            }     
        case 'LOCATION_360':
            return{
                ...state,
                locationName:state.payload,
                // showWishlist:false
            }     
        case '_360_NAME':
            return{
                ...state,
                _360Name:action.payload,
                // showWishlist:false
            }     
        case '_360_ID':
            return{
                ...state,
                _360Id:action.payload,
                // showWishlist:false
            }     
        case 'ID_NAME':
            return{
                ...state,
                infoID:action.payload,
                // showWishlist:false
            }     
        case 'POI_TYPE':
            return{
                ...state,
                POIType:action.payload,
                // showWishlist:false
            }     
        case 'LANDING_POI':
            return{
                ...state,
                landingPagePOI:true,
                // showWishlist:false
            }     
        case 'SHOW_POI':
            return{
                ...state,
                showPOI:true,
                // showWishlist:false
            }     
        case '360_POI':
            return{
                ...state,
                _360POI:true,
                // showWishlist:false
            }     
        case 'INFO_POI':
            return{
                ...state,
                infoPOI:true,
                // showWishlist:false
            }     
        case 'LEVEL_POI':
            return{
                ...state,
                levelPOI:true,
            }         
        case 'RESET_VIEW':
            return{
                ...state,
                restViewToggle:!state.restViewToggle,
            }     
        case 'RESET_STATE':
            return{
                ...state,
                landingPagePOI:false,
                _360POI:false,
                infoPOI:false,
                levelPOI:false,
            }     
        default:
                state
            break;
    }
}