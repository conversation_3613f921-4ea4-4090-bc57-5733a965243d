import React from 'react'
import <PERSON>E<PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUIEntranceComp({showIcons,handleStepIn}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
  const handleLeftLamp = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[5].name})
  }
  const handleRightLamp = (name) => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[0].name})
    experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[0].list[4].name})
  }
  
  return (
    <>
        <Html
            className={`${!experienceState?.showMenu ? 'flex scale-100' : 'hidden scale-0'} items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-8,-10,-20]}
            occlude
        >
            <BetaEntrance360Comp handleClick={handleStepIn}/>
        </Html>
        {/* RIGHT LAMP */}
        {showIcons && <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-14.25,-10,20]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleRightLamp}/>
        </Html>}
        {/* LEFT LAMP */}
        {showIcons && <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[24.5,-10,20]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleLeftLamp}/>
        </Html>}
    </>
  )
}
