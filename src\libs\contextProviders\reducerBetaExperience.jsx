export const INITIAL_EXPERIENCE_STATE={
    showCart:false,
    showMenu:false,
    _360Location:'entrance',
    _360Name:'entrance',
    showWishlist:false,
    landinPageItems:true,
    show360:false,
    closebtn:false,
    showNav:false,
    showInfo:false,
    texturedLoad:false,
    showBooknowMainMenu :false,
    rotationSnapValues:{},
    menuType:'',
}

export const ACTIONS_EXPERIENCE={
    TOGGLE_MENU:'TOGGLE_MENU',
    TOGGLE_INFO:'TOGGLE_INFO',
    SHOW_NAV:'SHOW_NAV',
    TOGGLE_CART:'TOGGLE_CART',
    TOGGLE_WISHLIST:'TOGGLE_WISHLIST',
    TOGGLE_LANDING_PAGE:'TOGGLE_LANDING_PAGE',
    LOCATION_360:'LOCATION_360', 
    LOAD_360:'LOAD_360', 
    LANDPAGE_BOOKNOW:'LANDPAGE_BOOKNOW', 
    SHOW_360:'SHOW_360', 
    RESET:'RESET', 
    _360_NAME:'360_NAME', 
    ENTRANCE:'ENTRANCE', 
    TEXTURES_LOADED:'TEXTURES_LOADED', 
    _360_SNAP_VALUES:'360_SNAP_VALUES', 
    MENU_TYPE:'MENU_TYPE', 
    ADD_TO_CART:'ADD_TO_CART'
}

export const reducerBetaExperience=(state,action)=>{
    switch (action.type) {
        case 'MENU_TYPE':
            return{
                ...state,
                menuType:action.payload,
                showMenu:true,
                showInfo:true
                // showWishlist:false
            }     
        case 'SHOW_NAV':
            return{
                ...state,
                showNav:true,
                // showWishlist:false
            }     
        case 'TOGGLE_MENU':
            return{
                ...state,
                showMenu:!state?.showMenu,
                showInfo:false,
                // showWishlist:false
            }     
        case 'TOGGLE_INFO':
            return{
                ...state,
                showMenu:!state?.showMenu,
                showInfo:true,
                menuType:action.payload
                // showWishlist:false
            }     
        case 'TOGGLE_CART':
            return{
                ...state,
                showCart:!state?.showCart,
                // showWishlist:false
            }     
        case 'TOGGLE_LANDING_PAGE':
            return{
                ...state,
                // showCart:false,
                landinPageItems:!state?.landinPageItems
            }
        case 'LOCATION_360':
            return{
                ...state,
                // showCart:false,
                _360Location:action.payload
            }
        case 'LOAD_360':
            return{
                ...state,
                // showCart:false,
                _360Name:action.payload
            }
        case 'SHOW_360':
            return{
                ...state,
                // showCart:false,
                landinPageItems:false,
                show360:true
            }
        case '360_SNAP_VALUES':
            return{
                ...state,
                // showCart:false,
                rotationSnapValues:action.payload
            }
        case 'TEXTURES_LOADED':
            return{
                ...state,
                // showCart:false,
                texturedLoad:true
            }
        case '360_NAME':
            return{
                ...state,
                // showCart:false,
                showMenu:false,
                _360Location:action.payload,
                _360Name:action.payload,
            }
        case 'TOGGLE_WISHLIST':
            return{
                ...state,
                // showCart:false,
                showWishlist:!state?.showWishlist
            }
        case 'ADD_TO_CART':
            return{
                ...state,
                // showCart:false,
            }
        case 'ENTRANCE':
            return{
                ...state,
                _360Location:'entrance',
                _360Name:'entrance',
                showNav:false,
                showMenu:false,
                // showCart:false,
            }
        case 'LANDPAGE_BOOKNOW':
            return{
                ...state,
                showBooknowMainMenu:!state?.showBooknowMainMenu
                // showCart:false,
            }
        case 'RESET':
            return{
                ...state,
                showCart:false,
                showMenu:false,
                _360Location:'entrance',
                _360Name:'entrance',
                showWishlist:false,
                landinPageItems:true,
                show360:false,
                closebtn:false,
                showNav:false,
                // showCart:false,
            }
        default:
                state
            break;
    }
}