import mongoose from 'mongoose';
const { Schema } = mongoose;

const _360Schema = new Schema({
    name:{type:String,required:true,unique:true},
    url:{type:String,required:true},
    path:{type:String,required:true},
    markerList:{type:Array},
    cameraPosition:{type:Number,default:0},
    _360Rotation:{type:Number,default:0},
},{timestamps:true});

export const _360Settings = mongoose.models._360Settings||mongoose.model('_360Settings', _360Schema)