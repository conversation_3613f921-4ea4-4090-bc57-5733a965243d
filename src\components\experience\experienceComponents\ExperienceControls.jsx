import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { OrbitControls } from '@react-three/drei'
import { useThree } from '@react-three/fiber'
import React, { useEffect, useRef, useState } from 'react'
import { degToRad } from 'three/src/math/MathUtils'

export default function ExperienceControls({}) {
    const {
      experienceStateList,setExperienceStateList,
      dataObject,
    }=useExperienceContext()
    
    const refControls=useRef()
    const {camera}=useThree()
    
    const centerCameraControlsView = (position) => {
      // console.log('centerCameraControlsView fn',position)
      camera.position.set(0,0,0)
      // camera.position.set(...position?.split(',').map(i=>parseFloat(i)))
      if(refControls.current){
        refControls.current.target.set(0,position,0)
        refControls.current.update()
      }
    }

    useEffect(() => {
        // console.log('ExperienceControls loaded:',dataObject)
        centerCameraControlsView(dataObject?.cameraPosition)
      }, [dataObject?.cameraPosition])
      
      console.log('ExperienceControls loaded:',camera)
      // console.log('ExperienceControls loaded:',refControls?.current?.target)
    // console.log('ExperienceControls:',dataObject?.cameraPosition)
  return (
    <OrbitControls
      ref={refControls}
      enablePan={false}
      maxPolarAngle={degToRad(120)}
      minPolarAngle={degToRad(60)}
      maxDistance={0.5}
      minDistance={0}
      enableDamping={true}
      target={[0, dataObject?.cameraPosition>=0 ? dataObject?.cameraPosition : 0, -1]}
      rotateSpeed={-.5}
    />
  )
}
