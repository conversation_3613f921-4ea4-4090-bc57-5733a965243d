import React from 'react'
import <PERSON><PERSON><PERSON><PERSON>360Comp from './BetaEntrance360Comp'
import { Html } from '@react-three/drei'
import BetaExperienceSnapPointComp from './BetaExperienceSnapPointComp'
import { settings } from '@/libs/betasiteSettings'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaUIMasterBed({handleClick}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()
   const habdleFirstfloor = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[0].name})
      }
  
      const handleBathRoom = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOCATION_360,payload:settings._360s[1].name})
        experienceDispatch({type:ACTIONS_EXPERIENCE.LOAD_360,payload:settings._360s[1].list[4].name})
      }
  
      const handleInfo = (name) => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_INFO,payload:'info'})
      }
  return (
    <>
         {/* STAIRCASE */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[-70,-10,-80]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={habdleFirstfloor}/>
        </Html>
         {/* INFO MATH */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[0,0,-80]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.readMore.height,width:settings.menuExp.readMore.width,image:settings.menuExp.readMore.btnIcons}} handleClick={handleInfo}/>
        </Html>
         {/* BATH ROOM */}
        <Html
            className={`items-center cursor-pointer justify-center w-40 h-40 duration-200 ease-linear text-white rounded-2xl`}
            position={[80,-47,-30]}
            occlude
        >
            <BetaExperienceSnapPointComp data={{height:settings.menuExp.guide.height,width:settings.menuExp.guide.width,image:settings.menuExp.guide.btnIcons}} handleClick={handleBathRoom}/>
        </Html>
    </>
  )
}
