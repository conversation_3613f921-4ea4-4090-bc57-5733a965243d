'use client'
import DashboardExperience360 from './DashboardExperience360'
import { TextureLoader } from 'three'
import { useLoader } from '@react-three/fiber'
import { useDashboardContext } from '@/libs/contextProviders/useContextDashboard'
import { useEffect } from 'react'

export default function DashboardExperienceTextureLoader() {
  const {
        experienceStateList,setExperienceStateList,
        dataObject,setDataObject,
        dataUpdate,setDataUpdate,
        refresh360
      }=useDashboardContext()

      let dataArray=[dataObject]

      let textureArrayList=[]
      let texturePathList=[]
      let textureNameList=[]
  
      dataArray?.map(i=>{
        textureArrayList.push(i?.name)
        texturePathList.push(i?.url)
        textureNameList.push(i?.name)
      })

      useEffect(() => {

      }, [refresh360])
      
  
      textureArrayList=useLoader(TextureLoader,texturePathList)
  
      textureArrayList.map((i,index)=>i.name=textureNameList[index])
  
      // console.log('ExperienceTextureLoader:',textureArrayList[0])
    return (
      <>
        {textureArrayList[0] && <DashboardExperience360 texture={textureArrayList[0]}/>}
      </>
  )
}
