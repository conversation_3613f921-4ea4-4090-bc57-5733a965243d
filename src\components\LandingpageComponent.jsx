'use client'
import { settings } from '@/libs/siteSettings'
import Image from 'next/image'
import React, { useState } from 'react'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'
import { useRouter } from 'next/navigation'

function BtnLandingpageComponent({data,fn,index}) {
    const [swap,setSwap]=useState(true)
    // console.log('BtnLandingpageComponent:',index)
    return(
        <div 
            onMouseEnter={()=>setSwap(!swap)} 
            onMouseLeave={()=>setSwap(!swap)} 
            className='btn flex relative items-center justify-center cursor-pointer max-w-fit max-h-fit'
        >
            <div 
                onClick={fn?.[index]} 
                className={`${swap ? 'hidden' : 'flex'} top-0 left-0 w-fit h-fit`}
            >
                <Image width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.ov}/>
            </div>
            <div
                onClick={fn?.[index]} 
                className={`${swap ? 'flex' : 'hidden'} top-0 left-0 w-fit h-fit`}
            >
                <Image width={data?.width} height={data?.height} alt='button images for landpage options' src={data?.btnIcons?.off}/>
            </div>
        </div>
    )
}

export default function LandingpageComponent() {
    const router=useRouter()
    const {experienceState,experienceDispatch}=useExperienceContext()

    const handleOnExplore = () => {
        router.push('/video')
        // console.log('explore')
    }
    const handleOnBookNow = () => {
        experienceDispatch({type:ACTIONS_EXPERIENCE.TOGGLE_LANDING_PAGE})
    }
    // console.log('LandingpageComponent:')
  return (
    <div className="flex flex-col items-center select-none absolute text-center left-0 right-0 mx-auto bg-black/75' justify-between w-fit h-full bg-black/25' p-2">
        <div className="flex relative mt-[7.5%] max-w-fit max-h-fit">
            <Image width={271} height={271} alt='elephant sands logo' src={settings.landingPage.logo}/>
        </div>

        <div className="flex relative bottom-24 items-center justify-between flex-col w-full h-[300px] bg-black/35'">
            <div className="flex relative mt-3 items-center flex-col uppercase mb-2">
                <span className="text-4xl">welcome to <span className='font-bold'>elephant island</span></span>
                <span className="font-bold tracking-wider text-lg">your home away from home experience</span>
            </div>
            <div className='flex flex-col items-center justify-center w-full h-fit'>
                <div className='flex items-center justify-center w-fit h-fit'>
                    {settings.landingPage.list.map((i,index)=>
                        // console.log(i)
                        <BtnLandingpageComponent 
                            key={index} 
                            data={i}
                            index={index}
                        />
                    )}
                </div>

                <div className='flex items-center justify-center w-fit h-40'>
                    {settings.landingPage.btns.map((i,index)=>
                        // console.log(i)
                        <BtnLandingpageComponent 
                            fn={[handleOnExplore,handleOnBookNow]} 
                            index={index}
                            key={index} 
                            data={i}
                        />
                    )}
                </div>
            </div>
        </div>
    </div>
  )
}
