'use client'

import { useDashboardContext } from "@/libs/contextProviders/useContextDashboard"
import { settings } from "@/libs/siteSettings"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { BsTrash } from "react-icons/bs"

function InputFileUpoald() {
    return(
        <div className="flex flex-col w-full h-fit rounded-md bg-gray-200 gap-2 p-2 shadow-md">
            <span className="flex font-medium capitalize text-xl">360 file uplaod</span>
            <div className="flex flex-col gap-2 w-full h-fit shadow rounded p-2 border-2 bg-gray-300 border-gray-400/25">
                <span className="flex flex-col text-sm capitalize">select file to uplaod</span>
                <span className="flex flex-col text-sm capitalize">0 uplaod</span>
                {settings.inputfields.inputFiles.map((i,index)=>
                    <div key={index} className={`flex justify-start w-full h-10 rounded bg-gray-100 shadow placeholder:text-sm text-sm px-2 items-center ${settings.inputfields.inputCss}`}>
                        <input {...i}/>
                    </div>
                )}
            </div>
        </div>
    )
}

function InputSearch() {
    const {
        inputList,setInputList,
        markerList,
        searchInput,setSearchInput,
        dataUpdate,setDataUpdate
    }=useDashboardContext()

    // const searchFunction = (e) => {
    //     console.log(e.target.value)
    //     // setSearchInput(e.target.value)
    // }
    
    // console.log('InputSearch:',dataUpdate?.filter((i,index)=>i?.name==e.target.value))
    return(
        <div className="flex flex-col w-full h-fit rounded-md bg-gray-200 gap-2 p-2 shadow-md">
            <span className="flex font-medium capitalize text-xl">360 search</span>
            <div className="flex flex-col gap-2 w-full h-fit shadow  rounded p-2 bg-gray-300 border-gray-400/25">
                <input 
                    onChange={e=>searchFunction(e)} 
                    className={`outline-none placeholder:text-sm px-2 shadow text-gray-500 placeholder:text-gray-500 ${settings.inputfields.inputCss}`} 
                    type="text" 
                    placeholder="Search 360"
                />
            </div>
        </div>
    )
}

function InputLocationList() {
    return(
        <div className="flex flex-col w-full h-fit rounded-md bg-gray-200 gap-2 p-2 shadow-md">
            <span className="flex font-medium capitalize text-xl">Location List</span>
            <div className="flex flex-col gap-2 w-full h-fit shadow rounded p-2 bg-gray-300 border-gray-400/25">
                <input className={`outline-none placeholder:text-sm shadow px-2 bg-gray-200 text-gray-500 placeholder:text-gray-500 ${settings.inputfields.inputCss}`} type="text" placeholder="Enter location"/>
            </div>
            <div className="flex flex-col gap-2 w-full h-fit shadow rounded p-2 bg-gray-300 border-gray-400/25">
                {settings.locationsList?.map((i,index)=>
                    <span className="text-sm" key={index}>{i}</span>
                )}
            </div>
        </div>
    )
}

function MarkerInput() {
    const {
        dataUpdate,setDataUpdate,
        markerObject,setMarkerObject,
        markerList,setMarkerList,
        inputList,setInputList
    }=useDashboardContext()

    {/* Add to add to component list list */}
    const addToList = (id) => {
        // console.log('addToList:',id)
        const objectFound=markerList?.map(i=>i?.name==id)
        objectFound[0] ? setMarkerList(markerList?.map(i=>i?.name==id ? {...i,...markerObject} : markerObject)) : setMarkerList([...markerList,markerObject])
    }

    {/* Add to add to global list list */}
    const addToMainList = () => {
        setMarkerList([...inputList])
    }

    console.log('MarkerInput markerObject:',markerObject)
    console.log('MarkerInput markerList:',markerList)
    
    return(
        <div className="flex flex-col w-full h-full gap-4 overflow-hidden">

            {/* Add to marker list */}
            <div className="flex flex-col w-full h-fit rounded-md bg-gray-200 gap-2 p-2 shadow-md">
                <span className="flex font-medium capitalize text-xl">Location List</span>

                <div className="flex gap-2 w-full h-fit shadow-md rounded p-2 bg-gray-300">
                    
                    {/* Marker name input */}
                    <input onChange={e=>setMarkerObject({...markerObject,name:e.target.value})} className={`outline-none capitalize placeholder:text-sm shadow px-2 text-gray-500 placeholder:text-gray-500 ${settings.inputfields.inputCss}`} type="text" placeholder="enter marker name"/>
                    
                    {/* marker type list */}
                    <select onChange={e=>setMarkerObject({...markerObject,markerType:e.target.value})} className={settings.inputfields.inputCss} name="" id="">
                        <option className={settings.inputfields.inputCss} value=''>marker type</option>
                        {settings.inputfields.markerType.map((i,index)=><option key={index} className={settings.inputfields.inputCss} value={i}>{i}</option>)}
                    </select>

                    {/* Add to marker list button */}
                    <button 
                        onClick={e=>addToList(markerObject?.name)} 
                        className={`flex w-fit uppercase text-xs cursor-pointer font-semibold px-4 h-10 rounded-md text-nowrap bg-gray-900 items-center justify-center text-white hover:bg-slate-600 duration-200 ease-linear shadow`}
                    >
                        add
                    </button>
                </div>

                {/* Delete the item in the marker list */}
                <div className="flex flex-wrap gap-2 w-full min-h-2 max-h-12 overflow-x-auto shadow-md rounded p-1 bg-gray-300">
                    {markerList?.map((i,index)=>
                        <div className="flex text-sm min-h-9 text-gray-600 max-h-10 gap-2 text-center text-nowrap capitalize justify-center items-center w-fit p-1 rounded border-2 border-gray-200 shadow" key={index}>
                            <span className="text-xs capitalizej">{i?.name}</span>
                            <BsTrash onClick={e=>setMarkerList(markerList?.filter(item=>item?.name!=i?.name))} className="flex text-xs text-gray-500 cursor-pointer h-full px-2 rounded shadow border-2 border-gray-200"/>
                        </div>
                    )}
                </div>
            </div>

            {/* List of Market Settings */}
            <div className="flex flex-col w-full h-fit rounded-md bg-gray-200 gap-2 p-2 shadow-md text-gray-600 overflow-y-auto">
                <span className="flex font-medium capitalize text-xl">Marker Options</span>
                {markerList?.map((i,index)=>
                    <div key={index} className="flex flex-col gap-2 w-full h-fit bordere-2 border-gray-50 shadow-md rounded p-2 bg-gray-300">
                        <span className="flex text-sm font-medium capitalize mb-2">{i?.name}</span>
                        <div className="flex flex-col w-full h-fit gap-2">
                            <div className="flex w-full h-fit gap-2">
                                
                                {/* Location name */}
                                <select onChange={e=>setMarkerObject({...markerObject,location:e.target.value})} className={settings.inputfields.inputCss} name="" id="">
                                    <option className={settings.inputfields.inputCss} value=''>select location</option>
                                    {settings.inputfields.locationsList.map((i,index)=><option key={index} className={settings.inputfields.inputCss} value={i}>{i}</option>)}
                                </select>

                                {/* marker type list */}
                                {/* <select onChange={e=>setMarkerObject({...markerObject,markerType:e.target.value})} className={settings.inputfields.inputCss} name="" id="">
                                    <option className={settings.inputfields.inputCss} value=''>marker type</option>
                                    {settings.inputfields.markerType.map((i,index)=><option key={index} className={settings.inputfields.inputCss} value={i}>{i}</option>)}
                                </select> */}
                                
                                {/* 360 list */}
                                {markerObject?.markerType?.includes('info') 
                                    ?   <select onChange={e=>setMarkerObject({...markerObject,_360Name:e.target.value})} className={settings.inputfields.inputCss} name="" id="">
                                            <option className={settings.inputfields.inputCss} value=''>select info type</option>
                                            {settings?.inputfields.contentType?.map((i,index)=><option key={index} className={settings.inputfields.inputCss} value={i}>{i}</option>)}
                                        </select>
                                    :   dataUpdate?.length>0 && <select onChange={e=>setMarkerObject({...markerObject,_360Name:e.target.value})} className={settings.inputfields.inputCss} name="" id="">
                                            <option className={settings.inputfields.inputCss} value=''>select 360</option>
                                            {dataUpdate?.map((i,index)=><option key={index} className={settings.inputfields.inputCss} value={i?.name}>{i?.name}</option>)}
                                        </select>
                                }
                            </div>

                        </div>

                        
                        {/* Add to marker list button */}
                        {/* <button 
                            onClick={addToMainList} 
                            className={`flex w-full uppercase text-xs cursor-pointer font-semibold px-4 min-h-10 max-h-10 rounded-md text-nowrap bg-gray-900 items-center justify-center text-white hover:bg-slate-600 duration-200 ease-linear shadow`}
                        >
                            add
                        </button> */}
                    </div>
                )}
            </div>
        </div>
    )
}

export default function DashboardInputWrapper({data}) {
    // console.log('DashboardInputWrapper:',data)
    const {
        inputList,setInputList,
        markerObject,dataObject,markerList
    }=useDashboardContext()

    const pathname=usePathname()

    const handleSubmit = () => {
      try {
        fetch(`${settings.url}/api/360s`,{
            method:'POST',
            headers:{
                'Content-Type':'application/json'
            },
            body:JSON.stringify({
                ...dataObject,markerList:markerList
            })
        })
      } catch (error) {
        console.log({error:error})
      }
    }
    const handleUpdate = () => {
        try {
            fetch(`${settings.url}/api/360s`,{
                method:'PATCH',
                headers:{
                    'Content-Type':'application/json'
                },
                body:JSON.stringify({
                    ...dataObject,markerList:markerList
                })
            })
        } catch (error) {
            console.log({error:error})
        }
    }

  console.log('DashboardWrapperSingle360 markerList:',markerList)
//   console.log('DashboardWrapperSingle360 dataObject:',{...dataObject,markerList:markerList})

  return (
    <div className='flex flex-col w-full h-full gap-3 justify-between overflow-hidden'>
        {pathname.split('/')?.length>2 
            ?   <div className="flex flex-col w-full h-fit gap-4 overflow-hidden">
                    <MarkerInput setInputList={setInputList} inputList={inputList}/>
                </div>
            :   <div className="flex flex-col w-full h-fit gap-4">
                    <InputFileUpoald/>
                    <InputSearch/>
                    <InputLocationList/>
                </div>
        }
        <button 
            onClick={data ? handleUpdate : handleSubmit} 
            className={`flex w-full uppercase text-sm font-semibold px-4 min-h-10 max-h-10 rounded-md text-nowrap bg-gray-900 items-center justify-center text-white hover:bg-slate-600 duration-200 ease-linear shadow`}
        >
            submit
        </button>
    </div>
  )
}
