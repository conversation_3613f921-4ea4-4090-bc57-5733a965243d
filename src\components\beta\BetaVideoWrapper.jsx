'use client'
import React, { useEffect, useRef } from 'react'
import VideoHeroComponent from '../VideoHeroComponent'
import { useBetaContextExperience } from '@/libs/contextProviders/useBetaContextExperience'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerBetaExperience'

export default function BetaVideoWrapper({data}) {
  const {experienceState,experienceDispatch}=useBetaContextExperience()

  const handleClick = () => {
    experienceDispatch({type:ACTIONS_EXPERIENCE.SHOW_360})
  }
  
  // console.log('VideoWrapper:',experienceState)
  return (
    <div className={`flex videoWrapper sm:h-dvh h-full w-full items-center justify-center`}>
      <div onClick={handleClick} className={`flex z-10 w-fit items-center justify-center text-xl px-10 rounded-full text-white capitalize h-12 cursor-pointer hover:bg-gray-800 duration-300 ease-linear absolute ${!experienceState?.landinPageItems ? 'top-10' : '-top-10'} left-0 right-0 mx-auto font-medium underline bg-black/50 border-2 border-gray-50`}>
        <span className='text-lg'>skip</span>
      </div>
      <VideoHeroComponent data={data}/>
    </div>
  )
}
