'use client'
import React, { useEffect, useRef } from 'react'
import VideoHeroComponent from './VideoHeroComponent'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { settings } from '@/libs/siteSettings'
import Link from 'next/link'

export default function VideoWrapper({data}) {
const shiow = (params) => {
  
}

  // console.log('VideoWrapper:',experienceState)
  return (
    <div className={`flex videoWrapper sm:h-dvh h-full w-full items-center justify-center`}>
      <Link href={'/beta2/360s'} className={`flex z-10 w-fit items-center justify-center text-xl px-10 rounded-full text-white capitalize h-12 cursor-pointer hover:bg-gray-800 duration-300 ease-linear absolute top-10 left-0 right-0 mx-auto font-medium underline bg-black/50 border-2 border-gray-50`}>
        <span className='text-lg'>skip</span>
      </Link>
      <VideoHeroComponent/>
    </div>
  )
}
